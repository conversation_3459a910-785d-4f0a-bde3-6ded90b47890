-- 患者提取结果表建表脚本
-- 创建时间: 2025-01-19
-- 用途: 存储4000患者的ETL提取结果和LLM思维链

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS patient_extractions;

-- 创建患者提取结果表
CREATE TABLE patient_extractions (
    patient_unique_id TEXT NOT NULL,           -- 患者唯一号（关联patient_basic_info.unique_id）
    task_code TEXT NOT NULL,                   -- 任务编号（关联ards_data_etl_rules.task_code）
    extraction_result TEXT,                    -- 提取结果
    llm_reasoning_chain TEXT,                  -- LLM思维链
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
    
    -- 复合主键
    PRIMARY KEY (patient_unique_id, task_code)
);

-- 添加表注释
COMMENT ON TABLE patient_extractions IS '患者提取结果表，存储每个患者每个任务的提取结果和LLM思维链';
COMMENT ON COLUMN patient_extractions.patient_unique_id IS '患者唯一号，关联patient_basic_info.unique_id';
COMMENT ON COLUMN patient_extractions.task_code IS '任务编号，关联ards_data_etl_rules.task_code';
COMMENT ON COLUMN patient_extractions.extraction_result IS '数据提取结果';
COMMENT ON COLUMN patient_extractions.llm_reasoning_chain IS 'LLM推理思维链，用于质量检查';
COMMENT ON COLUMN patient_extractions.created_time IS '记录创建时间';

-- 创建索引（优化查询性能）
-- 按患者查询优化
CREATE INDEX idx_patient_extractions_patient_id ON patient_extractions(patient_unique_id);

-- 按任务查询优化  
CREATE INDEX idx_patient_extractions_task_code ON patient_extractions(task_code);

-- 按时间查询优化
CREATE INDEX idx_patient_extractions_created_time ON patient_extractions(created_time);

-- 添加外键约束（确保数据完整性）
-- 注意：如果patient_basic_info表没有unique_id字段的唯一约束，需要先添加
ALTER TABLE patient_extractions 
ADD CONSTRAINT fk_patient_extractions_patient 
FOREIGN KEY (patient_unique_id) 
REFERENCES patient_basic_info(unique_id);

ALTER TABLE patient_extractions 
ADD CONSTRAINT fk_patient_extractions_task 
FOREIGN KEY (task_code) 
REFERENCES ards_data_etl_rules(task_code);

-- 验证表创建成功
SELECT 
    'patient_extractions表创建成功' as message,
    COUNT(*) as current_record_count 
FROM patient_extractions; 