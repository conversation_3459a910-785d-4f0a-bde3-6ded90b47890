﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 手术操作表
/// </summary>
public partial class operation_info
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    /// <summary>
    /// 病案号/住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 主要手术操作编码
    /// </summary>
    public string main_operation_code { get; set; }

    /// <summary>
    /// 主要手术操作名称
    /// </summary>
    public string main_operation_name { get; set; }

    /// <summary>
    /// 主要手术操作日期
    /// </summary>
    public string main_operation_date { get; set; }

    /// <summary>
    /// 主要手术操作级别
    /// </summary>
    public string main_operation_level { get; set; }

    /// <summary>
    /// 主要手术操作持续时间
    /// </summary>
    public string main_operation_duration { get; set; }

    /// <summary>
    /// 主要手术操作术者
    /// </summary>
    public string main_operation_surgeon { get; set; }

    /// <summary>
    /// 主要手术操作Ⅰ助
    /// </summary>
    public string main_operation_first_assistant { get; set; }

    /// <summary>
    /// 主要手术操作Ⅱ助
    /// </summary>
    public string main_operation_second_assistant { get; set; }

    /// <summary>
    /// 主要手术操作切口愈合等级
    /// </summary>
    public string main_operation_wound_healing_grade { get; set; }

    /// <summary>
    /// 主要手术操作麻醉方式
    /// </summary>
    public string main_operation_anesthesia_method { get; set; }

    /// <summary>
    /// 主要手术操作麻醉分级
    /// </summary>
    public string main_operation_anesthesia_level { get; set; }

    /// <summary>
    /// 主要手术操作麻醉医师
    /// </summary>
    public string main_operation_anesthesiologist { get; set; }

    /// <summary>
    /// 其他手术操作编码数组
    /// </summary>
    public List<string> other_operation_codes { get; set; }

    /// <summary>
    /// 其他手术操作名称数组
    /// </summary>
    public List<string> other_operation_names { get; set; }

    /// <summary>
    /// 其他手术操作日期数组
    /// </summary>
    public List<string> other_operation_dates { get; set; }

    /// <summary>
    /// 其他手术操作级别数组
    /// </summary>
    public List<string> other_operation_levels { get; set; }

    /// <summary>
    /// 其他手术操作持续时间数组
    /// </summary>
    public List<string> other_operation_durations { get; set; }

    /// <summary>
    /// 其他手术操作术者数组
    /// </summary>
    public List<string> other_operation_surgeons { get; set; }

    /// <summary>
    /// 其他手术操作Ⅰ助数组
    /// </summary>
    public List<string> other_operation_first_assistants { get; set; }

    /// <summary>
    /// 其他手术操作Ⅱ助数组
    /// </summary>
    public List<string> other_operation_second_assistants { get; set; }

    /// <summary>
    /// 其他手术操作切口愈合等级数组
    /// </summary>
    public List<string> other_operation_wound_healing_grades { get; set; }

    /// <summary>
    /// 其他手术操作麻醉方式数组
    /// </summary>
    public List<string> other_operation_anesthesia_methods { get; set; }

    /// <summary>
    /// 其他手术操作麻醉分级数组
    /// </summary>
    public List<string> other_operation_anesthesia_levels { get; set; }

    /// <summary>
    /// 其他手术操作麻醉医师数组
    /// </summary>
    public List<string> other_operation_anesthesiologists { get; set; }

    public virtual patient_base link { get; set; }
}