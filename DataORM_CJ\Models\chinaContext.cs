﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace DataORM_CJ.Models;

public partial class chinaContext : DbContext
{
    public chinaContext(DbContextOptions<chinaContext> options)
        : base(options)
    {
    }

    public virtual DbSet<ards_data_etl_rules> ards_data_etl_rules { get; set; }

    public virtual DbSet<blood_transfusion> blood_transfusion { get; set; }

    public virtual DbSet<cost_info> cost_info { get; set; }

    public virtual DbSet<diagnosis_info> diagnosis_info { get; set; }

    public virtual DbSet<electronic_medical_record> electronic_medical_record { get; set; }

    public virtual DbSet<hospitalization_record> hospitalization_record { get; set; }

    public virtual DbSet<icu_record> icu_record { get; set; }

    public virtual DbSet<imaging_examination_record> imaging_examination_record { get; set; }

    public virtual DbSet<laboratory_test_record> laboratory_test_record { get; set; }

    public virtual DbSet<medical_cost_detail> medical_cost_detail { get; set; }

    public virtual DbSet<medical_order> medical_order { get; set; }

    public virtual DbSet<medical_record> medical_record { get; set; }

    public virtual DbSet<medical_record_front_page> medical_record_front_page { get; set; }

    public virtual DbSet<nursing_level> nursing_level { get; set; }

    public virtual DbSet<nursing_record> nursing_record { get; set; }

    public virtual DbSet<operation_info> operation_info { get; set; }

    public virtual DbSet<patient_base> patient_base { get; set; }

    public virtual DbSet<patient_basic_info> patient_basic_info { get; set; }

    public virtual DbSet<patient_blood_gas_results> patient_blood_gas_results { get; set; }

    public virtual DbSet<patient_diagnosis> patient_diagnosis { get; set; }

    public virtual DbSet<patient_extractions> patient_extractions { get; set; }

    public virtual DbSet<patient_progress_record> patient_progress_record { get; set; }

    public virtual DbSet<respiratory_support> respiratory_support { get; set; }

    public virtual DbSet<surgical_operation> surgical_operation { get; set; }

    public virtual DbSet<surgical_progress_record> surgical_progress_record { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasPostgresExtension("uuid-ossp");

        modelBuilder.Entity<ards_data_etl_rules>(entity =>
        {
            entity.HasKey(e => e.task_code).HasName("ards_data_etl_rules_pkey");

            entity.ToTable("ards_data_etl_rules", "etl", tb => tb.HasComment("中日友好医院ARDS数据清洗规则表"));

            entity.Property(e => e.task_code).HasComment("任务编号，如ETL_CJ_101");
            entity.Property(e => e.dict_name).HasComment("数据字典名称");
            entity.Property(e => e.extract_location).HasComment("数据提取位置说明");
            entity.Property(e => e.is_extract).HasComment("是否提取标识，如\"本次需提取\"");
            entity.Property(e => e.prompt).HasComment("LLM提取prompt");
            entity.Property(e => e.remark).HasComment("详细备注和提取规则");
            entity.Property(e => e.sql_script).HasComment("数据需求");
        });

        modelBuilder.Entity<blood_transfusion>(entity =>
        {
            entity.HasKey(e => new { e.link_id, e.record_id, e.admission_time, e.blood_type }).HasName("pk_blood_transfusion");

            entity.ToTable(tb => tb.HasComment("输血记录表"));

            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.record_id).HasComment("住院号/门诊号");
            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.blood_type).HasComment("血液类型");
            entity.Property(e => e.amount).HasComment("数量");
            entity.Property(e => e.reaction).HasComment("输血反应");

            entity.HasOne(d => d.link).WithMany(p => p.blood_transfusion)
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_blood_transfusion_link_id");
        });

        modelBuilder.Entity<cost_info>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable(tb => tb.HasComment("费用信息表"));

            entity.Property(e => e.albumin_product_fee).HasComment("17.白蛋白类制品费");
            entity.Property(e => e.anesthesia_fee).HasComment("其中：麻醉费");
            entity.Property(e => e.antibacterial_drug_fee).HasComment("其中：抗菌药物费");
            entity.Property(e => e.blood_fee).HasComment("16.血费");
            entity.Property(e => e.chinese_herbal_medicine_fee).HasComment("15.中草药费");
            entity.Property(e => e.chinese_patent_medicine_fee).HasComment("14.中成药费");
            entity.Property(e => e.clinical_diagnosis_project_fee).HasComment("8.临床诊断项目费");
            entity.Property(e => e.clinical_physical_therapy_fee).HasComment("其中：临床物理治疗费");
            entity.Property(e => e.coagulation_factor_product_fee).HasComment("19.凝血因子类制品费");
            entity.Property(e => e.cytokine_product_fee).HasComment("20.细胞因子类制品费");
            entity.Property(e => e.examination_disposable_medical_material_fee).HasComment("21.检查用一次性医用材料费");
            entity.Property(e => e.general_medical_service_fee).HasComment("1.一般医疗服务费");
            entity.Property(e => e.general_treatment_operation_fee).HasComment("2.一般治疗操作费");
            entity.Property(e => e.globulin_product_fee).HasComment("18.球蛋白类制品费");
            entity.Property(e => e.imaging_diagnosis_fee).HasComment("7.影像学诊断费");
            entity.Property(e => e.laboratory_diagnosis_fee).HasComment("6.实验室诊断费");
            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.non_surgical_treatment_project_fee).HasComment("9.非手术治疗项目费");
            entity.Property(e => e.nursing_fee).HasComment("3.护理费");
            entity.Property(e => e.other_comprehensive_medical_service_fee).HasComment("4.综合医疗服务类其他费用");
            entity.Property(e => e.other_fee).HasComment("24.其他费");
            entity.Property(e => e.pathological_diagnosis_fee).HasComment("5.病理诊断费");
            entity.Property(e => e.record_id).HasComment("病案号/住院号/门诊号");
            entity.Property(e => e.rehabilitation_fee).HasComment("11.康复费");
            entity.Property(e => e.self_payment_amount).HasComment("住院总费用其中自付金额");
            entity.Property(e => e.surgery_fee).HasComment("其中：手术费");
            entity.Property(e => e.surgical_disposable_medical_material_fee).HasComment("23.手术用一次性医用材料费");
            entity.Property(e => e.surgical_treatment_fee).HasComment("10.手术治疗费");
            entity.Property(e => e.total_cost).HasComment("住院总费用");
            entity.Property(e => e.traditional_chinese_medicine_treatment_fee).HasComment("12.中医治疗费");
            entity.Property(e => e.treatment_disposable_medical_material_fee).HasComment("22.治疗用一次性医用材料费");
            entity.Property(e => e.unique_id).HasComment("唯一号");
            entity.Property(e => e.western_medicine_fee).HasComment("13.西药费");

            entity.HasOne(d => d.link).WithMany()
                .HasForeignKey(d => d.link_id)
                .HasConstraintName("fk_cost_info_link_id");
        });

        modelBuilder.Entity<diagnosis_info>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable(tb => tb.HasComment("诊断信息表"));

            entity.Property(e => e.admission_diagnosis_code).HasComment("入院诊断编码");
            entity.Property(e => e.admission_diagnosis_name).HasComment("入院诊断名称");
            entity.Property(e => e.confirmed_diagnosis_date).HasComment("入院后确诊日期");
            entity.Property(e => e.injury_poisoning_external_cause_code).HasComment("损伤、中毒外部原因编码");
            entity.Property(e => e.injury_poisoning_external_cause_name).HasComment("损伤、中毒外部原因名称");
            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.main_discharge_diagnosis_admission_condition).HasComment("出院主要诊断入院病情");
            entity.Property(e => e.main_discharge_diagnosis_code).HasComment("出院主要诊断编码");
            entity.Property(e => e.main_discharge_diagnosis_discharge_condition).HasComment("主要诊断出院情况");
            entity.Property(e => e.main_discharge_diagnosis_name).HasComment("出院主要诊断名称");
            entity.Property(e => e.other_diagnosis_admission_conditions).HasComment("出院其他诊断入院病情数组");
            entity.Property(e => e.other_diagnosis_codes).HasComment("出院其他诊断编码数组");
            entity.Property(e => e.other_diagnosis_discharge_conditions).HasComment("出院其他诊断出院情况数组");
            entity.Property(e => e.other_diagnosis_names).HasComment("出院其他诊断名称数组");
            entity.Property(e => e.pathological_diagnosis_codes).HasComment("病理诊断编码数组");
            entity.Property(e => e.pathological_diagnosis_names).HasComment("病理诊断名称数组");
            entity.Property(e => e.pathological_numbers).HasComment("病理号数组");
            entity.Property(e => e.record_id).HasComment("病案号/住院号/门诊号");
            entity.Property(e => e.unique_id).HasComment("唯一号");

            entity.HasOne(d => d.link).WithMany()
                .HasForeignKey(d => d.link_id)
                .HasConstraintName("fk_diagnosis_info_link_id");
        });

        modelBuilder.Entity<electronic_medical_record>(entity =>
        {
            entity.HasKey(e => new { e.link_id, e.record_id, e.admission_time }).HasName("pk_electronic_medical_record");

            entity.ToTable(tb => tb.HasComment("患者电子病历表"));

            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.record_id).HasComment("住院号/门诊号");
            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.admission_diagnosis).HasComment("入院诊断");
            entity.Property(e => e.age).HasComment("年龄");
            entity.Property(e => e.allergy_history).HasComment("过敏史");
            entity.Property(e => e.birth_date).HasComment("出生日期");
            entity.Property(e => e.chief_complaint).HasComment("主诉");
            entity.Property(e => e.discharge_diagnosis).HasComment("出院诊断");
            entity.Property(e => e.discharge_record).HasComment("出院记录");
            entity.Property(e => e.discharge_time).HasComment("出院时间");
            entity.Property(e => e.family_history).HasComment("家族史");
            entity.Property(e => e.gender)
                .IsRequired()
                .HasComment("性别");
            entity.Property(e => e.marriage_history).HasComment("婚育史");
            entity.Property(e => e.menstrual_history).HasComment("月经史");
            entity.Property(e => e.past_history).HasComment("既往史");
            entity.Property(e => e.patient_name)
                .IsRequired()
                .HasComment("姓名");
            entity.Property(e => e.personal_history).HasComment("个人史");
            entity.Property(e => e.physical_examination).HasComment("体格检查");
            entity.Property(e => e.present_illness).HasComment("现病史");
            entity.Property(e => e.unique_id)
                .IsRequired()
                .HasComment("唯一号");

            entity.HasOne(d => d.link).WithMany(p => p.electronic_medical_record)
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_electronic_medical_record_link_id");
        });

        modelBuilder.Entity<hospitalization_record>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable(tb => tb.HasComment("住院记录表"));

            entity.Property(e => e.actual_hospitalization_days).HasComment("实际住院（天）");
            entity.Property(e => e.admission_condition).HasComment("入院时情况");
            entity.Property(e => e.admission_department).HasComment("入院科别");
            entity.Property(e => e.admission_path).HasComment("入院途径");
            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.admission_ward).HasComment("入院病房");
            entity.Property(e => e.age_under_one_week_days).HasComment("年龄不足1周岁的年龄（天）");
            entity.Property(e => e.attending_physician).HasComment("主治医师");
            entity.Property(e => e.attending_physician_execution_number).HasComment("主治医师执行编号");
            entity.Property(e => e.autologous_blood_transfusion).HasComment("自体血回输");
            entity.Property(e => e.blood_transfusion_reaction).HasComment("输血反应");
            entity.Property(e => e.chief_physician).HasComment("主（副主）任医师");
            entity.Property(e => e.chief_physician_execution_number).HasComment("主（副主）任医师执行编号");
            entity.Property(e => e.coder).HasComment("编码员");
            entity.Property(e => e.craniocerebral_injury_after_coma_days).HasComment("颅脑损伤患者入院后昏迷时间（天）");
            entity.Property(e => e.craniocerebral_injury_after_coma_hours).HasComment("颅脑损伤患者入院后昏迷时间（小时）");
            entity.Property(e => e.craniocerebral_injury_after_coma_minutes).HasComment("颅脑损伤患者入院后昏迷时间（分钟）");
            entity.Property(e => e.craniocerebral_injury_before_coma_days).HasComment("颅脑损伤患者入院前昏迷时间（天）");
            entity.Property(e => e.craniocerebral_injury_before_coma_hours).HasComment("颅脑损伤患者入院前昏迷时间（小时）");
            entity.Property(e => e.craniocerebral_injury_before_coma_minutes).HasComment("颅脑损伤患者入院前昏迷时间（分钟）");
            entity.Property(e => e.death_patient_autopsy).HasComment("死亡患者尸检");
            entity.Property(e => e.department_director).HasComment("科主任");
            entity.Property(e => e.department_director_execution_number).HasComment("科主任执行编号");
            entity.Property(e => e.discharge_department).HasComment("出院科别");
            entity.Property(e => e.discharge_method).HasComment("离院方式");
            entity.Property(e => e.discharge_time).HasComment("出院时间");
            entity.Property(e => e.discharge_ward).HasComment("出院病房");
            entity.Property(e => e.emergency_diagnosis_code).HasComment("门（急）诊诊断编码");
            entity.Property(e => e.emergency_diagnosis_name).HasComment("门（急）诊诊断名称");
            entity.Property(e => e.first_level_care_days).HasComment("一级护理天数");
            entity.Property(e => e.has_rehospitalization_plan).HasComment("是否有出院31日内再住院计划");
            entity.Property(e => e.hospitalization_count).HasComment("住院次数");
            entity.Property(e => e.icu_entry_times).HasComment("重症监护室进入时间数组");
            entity.Property(e => e.icu_exit_times).HasComment("重症监护室退出时间数组");
            entity.Property(e => e.icu_names).HasComment("重症监护室名称数组");
            entity.Property(e => e.intern_physician).HasComment("实习医师");
            entity.Property(e => e.invasive_ventilator_time).HasComment("有创呼吸机使用时间");
            entity.Property(e => e.is_day_surgery).HasComment("是否为日间手术");
            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.medical_institution_name).HasComment("医疗机构名称");
            entity.Property(e => e.medical_record_quality).HasComment("病案质量");
            entity.Property(e => e.newborn_admission_weight).HasComment("新生儿入院体重（克）");
            entity.Property(e => e.newborn_birth_weights).HasComment("新生儿出生体重（克）数组，最多5个");
            entity.Property(e => e.organization_code).HasComment("组织机构代码");
            entity.Property(e => e.payment_method).HasComment("医疗付费方式");
            entity.Property(e => e.plasma).HasComment("血浆");
            entity.Property(e => e.platelets).HasComment("血小板");
            entity.Property(e => e.quality_control_date).HasComment("质控日期");
            entity.Property(e => e.quality_control_nurse).HasComment("质控护师");
            entity.Property(e => e.quality_control_nurse_execution_code).HasComment("质控护士执行代码");
            entity.Property(e => e.quality_control_physician).HasComment("质控医师");
            entity.Property(e => e.record_id).HasComment("病案号/住院号/门诊号");
            entity.Property(e => e.red_blood_cells).HasComment("红细胞");
            entity.Property(e => e.rehospitalization_plan_purpose).HasComment("出院31天再住院计划目的");
            entity.Property(e => e.resident_physician).HasComment("住院医师");
            entity.Property(e => e.resident_physician_execution_number).HasComment("住院医师执行编号");
            entity.Property(e => e.responsible_nurse).HasComment("责任护士");
            entity.Property(e => e.responsible_nurse_execution_number).HasComment("责任护士执行编号");
            entity.Property(e => e.second_level_care_days).HasComment("二级护理天数");
            entity.Property(e => e.special_care_days).HasComment("特级护理天数");
            entity.Property(e => e.third_level_care_days).HasComment("三级护理天数");
            entity.Property(e => e.transfer_department).HasComment("转科科别");
            entity.Property(e => e.transfer_institution_name).HasComment("医嘱转院、转社区卫生服务机构/乡镇卫生院名称");
            entity.Property(e => e.unique_id).HasComment("唯一号");
            entity.Property(e => e.visiting_physician).HasComment("进修医师");
            entity.Property(e => e.whole_blood).HasComment("全血");

            entity.HasOne(d => d.link).WithMany()
                .HasForeignKey(d => d.link_id)
                .HasConstraintName("fk_hospitalization_record_link_id");
        });

        modelBuilder.Entity<icu_record>(entity =>
        {
            entity.HasKey(e => new { e.link_id, e.record_id, e.admission_time, e.icu_seq }).HasName("pk_icu_record");

            entity.ToTable(tb => tb.HasComment("重症监护记录表"));

            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.record_id).HasComment("住院号/门诊号");
            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.icu_seq).HasComment("重症监护序号");
            entity.Property(e => e.icu_enter_time)
                .HasComment("重症监护室进入时间")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.icu_exit_time)
                .HasComment("重症监护室退出时间")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.icu_name).HasComment("重症监护室名称");

            entity.HasOne(d => d.link).WithMany(p => p.icu_record)
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_icu_record_link_id");
        });

        modelBuilder.Entity<imaging_examination_record>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable(tb => tb.HasComment("影像学检查数据表"));

            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.age).HasComment("年龄");
            entity.Property(e => e.birth_date).HasComment("出生日期");
            entity.Property(e => e.conclusion)
                .IsRequired()
                .HasComment("检查结论");
            entity.Property(e => e.device_type)
                .IsRequired()
                .HasComment("检查设备类型");
            entity.Property(e => e.discharge_time).HasComment("出院时间");
            entity.Property(e => e.examination_item)
                .IsRequired()
                .HasComment("检查项目");
            entity.Property(e => e.examination_no)
                .IsRequired()
                .HasComment("检查号");
            entity.Property(e => e.examination_part)
                .IsRequired()
                .HasComment("检查部位");
            entity.Property(e => e.examination_time)
                .HasComment("检查时间")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.gender)
                .IsRequired()
                .HasComment("性别");
            entity.Property(e => e.imaging_finding).HasComment("影像所见");
            entity.Property(e => e.link_id)
                .IsRequired()
                .HasComment("关联号");
            entity.Property(e => e.patient_name)
                .IsRequired()
                .HasComment("姓名");
            entity.Property(e => e.record_id)
                .IsRequired()
                .HasComment("住院号/门诊号");
            entity.Property(e => e.referring_department)
                .IsRequired()
                .HasComment("送检科室名称");
            entity.Property(e => e.unique_id).HasComment("唯一号");

            entity.HasOne(d => d.link).WithMany()
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_imaging_examination_record_link_id");
        });

        modelBuilder.Entity<laboratory_test_record>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable(tb => tb.HasComment("实验室检查数据表"));

            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.age).HasComment("年龄");
            entity.Property(e => e.birth_date).HasComment("出生日期");
            entity.Property(e => e.discharge_time).HasComment("出院时间");
            entity.Property(e => e.english_name).HasComment("英文名称");
            entity.Property(e => e.gender)
                .IsRequired()
                .HasComment("性别");
            entity.Property(e => e.indicator_name)
                .IsRequired()
                .HasComment("检验指标名");
            entity.Property(e => e.indicator_value)
                .IsRequired()
                .HasComment("检验指标值");
            entity.Property(e => e.link_id)
                .IsRequired()
                .HasComment("关联号");
            entity.Property(e => e.patient_name)
                .IsRequired()
                .HasComment("姓名");
            entity.Property(e => e.record_id)
                .IsRequired()
                .HasComment("住院号/门诊号");
            entity.Property(e => e.reference_range).HasComment("参考范围");
            entity.Property(e => e.report_date)
                .HasComment("报告日期")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.test_item)
                .IsRequired()
                .HasComment("检验项目");
            entity.Property(e => e.unique_id).HasComment("唯一号");
            entity.Property(e => e.unit).HasComment("单位");

            entity.HasOne(d => d.link).WithMany()
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_laboratory_test_record_link_id");
        });

        modelBuilder.Entity<medical_cost_detail>(entity =>
        {
            entity.HasKey(e => new { e.link_id, e.record_id, e.admission_time, e.cost_type }).HasName("pk_medical_cost_detail");

            entity.ToTable(tb => tb.HasComment("患者费用明细表"));

            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.record_id).HasComment("住院号/门诊号");
            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.cost_type).HasComment("费用类型");
            entity.Property(e => e.cost_amount)
                .HasPrecision(12, 2)
                .HasComment("费用金额");

            entity.HasOne(d => d.link).WithMany(p => p.medical_cost_detail)
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_medical_cost_detail_link_id");
        });

        modelBuilder.Entity<medical_order>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable(tb => tb.HasComment("医嘱数据表"));

            entity.HasIndex(e => e.order_name, "idx_medical_order_order_name");

            entity.HasIndex(e => e.patient_name, "idx_medical_order_patient_name");

            entity.HasIndex(e => e.record_id, "idx_medical_order_record_id");

            entity.HasIndex(e => new { e.record_id, e.start_time }, "idx_medical_order_record_id_start_time");

            entity.HasIndex(e => e.start_time, "idx_medical_order_start_time");

            entity.HasIndex(e => e.unique_id, "idx_medical_order_unique_id");

            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.age).HasComment("年龄");
            entity.Property(e => e.birth_date).HasComment("出生日期");
            entity.Property(e => e.discharge_time).HasComment("出院时间");
            entity.Property(e => e.dosage).HasComment("剂量");
            entity.Property(e => e.end_time)
                .HasComment("结束时间")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.frequency).HasComment("频次");
            entity.Property(e => e.gender)
                .IsRequired()
                .HasComment("性别");
            entity.Property(e => e.link_id)
                .IsRequired()
                .HasComment("关联号");
            entity.Property(e => e.order_name)
                .IsRequired()
                .HasComment("医嘱名称");
            entity.Property(e => e.patient_name)
                .IsRequired()
                .HasComment("姓名");
            entity.Property(e => e.record_id)
                .IsRequired()
                .HasComment("住院号/门诊号");
            entity.Property(e => e.start_time)
                .HasComment("开始时间")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.unique_id).HasComment("唯一号");
            entity.Property(e => e.usage_method).HasComment("用法");

            entity.HasOne(d => d.link).WithMany()
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_medical_order_link_id");
        });

        modelBuilder.Entity<medical_record>(entity =>
        {
            entity.HasKey(e => new { e.link_id, e.record_id, e.admission_time }).HasName("pk_medical_record");

            entity.ToTable(tb => tb.HasComment("患者就诊记录表"));

            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.record_id).HasComment("住院号/门诊号");
            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.department_id)
                .IsRequired()
                .HasComment("就诊科室号");
            entity.Property(e => e.department_name)
                .IsRequired()
                .HasComment("就诊科室名称");
            entity.Property(e => e.discharge_time).HasComment("出院时间");

            entity.HasOne(d => d.link).WithMany(p => p.medical_record)
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_medical_record_link_id");
        });

        modelBuilder.Entity<medical_record_front_page>(entity =>
        {
            entity.HasKey(e => new { e.link_id, e.record_id, e.admission_time }).HasName("pk_medical_record_front_page");

            entity.ToTable(tb => tb.HasComment("患者病案首页表"));

            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.record_id).HasComment("住院号/门诊号");
            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.actual_days_in_hospital).HasComment("实际住院（天）");
            entity.Property(e => e.admission_department).HasComment("入院科别");
            entity.Property(e => e.admission_diagnosis_code).HasComment("入院诊断编码");
            entity.Property(e => e.admission_diagnosis_name).HasComment("入院诊断名称");
            entity.Property(e => e.admission_route).HasComment("入院途径");
            entity.Property(e => e.admission_ward).HasComment("入院病房");
            entity.Property(e => e.age).HasComment("年龄");
            entity.Property(e => e.allergic_drug_flag).HasComment("有无药物过敏");
            entity.Property(e => e.allergic_drug_name).HasComment("过敏药物名称");
            entity.Property(e => e.attending_doctor_code).HasComment("主（副主）任医师执行编号");
            entity.Property(e => e.attending_doctor_name).HasComment("主（副主）任医师");
            entity.Property(e => e.birth_address).HasComment("出生地址");
            entity.Property(e => e.birth_date).HasComment("出生日期");
            entity.Property(e => e.blood_type_abo).HasComment("ABO血型");
            entity.Property(e => e.blood_type_rh).HasComment("Rh血型");
            entity.Property(e => e.condition_on_admission).HasComment("入院时情况");
            entity.Property(e => e.confirmed_diagnosis_date).HasComment("入院后确诊日期");
            entity.Property(e => e.contact_address).HasComment("联系人地址");
            entity.Property(e => e.contact_name).HasComment("联系人姓名");
            entity.Property(e => e.contact_phone).HasComment("联系人电话");
            entity.Property(e => e.contact_relationship).HasComment("联系人关系");
            entity.Property(e => e.current_address).HasComment("现住址");
            entity.Property(e => e.current_address_phone).HasComment("现住址电话");
            entity.Property(e => e.current_address_postcode).HasComment("现住址邮政编码");
            entity.Property(e => e.days_for_under_one_year).HasComment("年龄不足1周岁的年龄（天）");
            entity.Property(e => e.director_code).HasComment("科主任执行编号");
            entity.Property(e => e.director_name).HasComment("科主任");
            entity.Property(e => e.discharge_department).HasComment("出院科别");
            entity.Property(e => e.discharge_main_diagnosis_code).HasComment("出院主要诊断编码");
            entity.Property(e => e.discharge_main_diagnosis_name).HasComment("出院主要诊断名称");
            entity.Property(e => e.discharge_time).HasComment("出院时间");
            entity.Property(e => e.discharge_ward).HasComment("出院病房");
            entity.Property(e => e.emergency_diagnosis_code).HasComment("门（急）诊诊断编码");
            entity.Property(e => e.emergency_diagnosis_name).HasComment("门（急）诊诊断名称");
            entity.Property(e => e.ethnicity).HasComment("民族");
            entity.Property(e => e.gender)
                .IsRequired()
                .HasComment("性别");
            entity.Property(e => e.hospital_name).HasComment("医疗机构名称");
            entity.Property(e => e.hospitalization_number).HasComment("住院次数");
            entity.Property(e => e.id_number).HasComment("身份证号");
            entity.Property(e => e.id_type).HasComment("证件类别");
            entity.Property(e => e.is_day_surgery).HasComment("是否为日间手术");
            entity.Property(e => e.leave_type).HasComment("离院方式");
            entity.Property(e => e.main_diagnosis_condition_on_admission).HasComment("主要诊断入院病情");
            entity.Property(e => e.main_diagnosis_condition_on_discharge).HasComment("主要诊断出院情况");
            entity.Property(e => e.marital_status).HasComment("婚姻");
            entity.Property(e => e.medical_record_number).HasComment("病案号");
            entity.Property(e => e.nationality).HasComment("国籍");
            entity.Property(e => e.occupation).HasComment("职业");
            entity.Property(e => e.organization_code).HasComment("组织机构代码");
            entity.Property(e => e.patient_name)
                .IsRequired()
                .HasComment("姓名");
            entity.Property(e => e.payment_method).HasComment("医疗付费方式");
            entity.Property(e => e.quality_control_date).HasComment("质控日期");
            entity.Property(e => e.quality_control_doctor).HasComment("质控医师");
            entity.Property(e => e.quality_control_nurse).HasComment("质控护师");
            entity.Property(e => e.readmission_plan_in_31_days).HasComment("是否有出院31日内再住院计划");
            entity.Property(e => e.readmission_plan_purpose).HasComment("出院31天再住院计划目的");
            entity.Property(e => e.registered_residence).HasComment("户口地址");
            entity.Property(e => e.registered_residence_postcode).HasComment("户口地址邮政编码");
            entity.Property(e => e.resident_doctor_code).HasComment("住院医师执行编号");
            entity.Property(e => e.resident_doctor_name).HasComment("住院医师");
            entity.Property(e => e.responsible_nurse_code).HasComment("责任护士执行编号");
            entity.Property(e => e.responsible_nurse_name).HasComment("责任护士");
            entity.Property(e => e.self_payment)
                .HasPrecision(12, 2)
                .HasComment("住院总费用其中自付金额");
            entity.Property(e => e.total_cost)
                .HasPrecision(12, 2)
                .HasComment("住院总费用");
            entity.Property(e => e.transfer_department).HasComment("转科科别");
            entity.Property(e => e.transfer_hospital_name).HasComment("医嘱转院、转社区卫生服务机构/乡镇卫生院名称");
            entity.Property(e => e.unique_id)
                .IsRequired()
                .HasComment("唯一号");
            entity.Property(e => e.work_unit_address).HasComment("工作单位及地址");
            entity.Property(e => e.work_unit_phone).HasComment("工作单位电话");
            entity.Property(e => e.work_unit_postcode).HasComment("工作单位邮政编码");

            entity.HasOne(d => d.link).WithMany(p => p.medical_record_front_page)
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_medical_record_front_page_link_id");
        });

        modelBuilder.Entity<nursing_level>(entity =>
        {
            entity.HasKey(e => new { e.link_id, e.record_id, e.admission_time, e.nursing_level_type }).HasName("pk_nursing_level");

            entity.ToTable(tb => tb.HasComment("护理等级记录表"));

            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.record_id).HasComment("住院号/门诊号");
            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.nursing_level_type).HasComment("护理等级类型");
            entity.Property(e => e.days).HasComment("天数");

            entity.HasOne(d => d.link).WithMany(p => p.nursing_level)
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_nursing_level_link_id");
        });

        modelBuilder.Entity<nursing_record>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable(tb => tb.HasComment("患者护理记录表"));

            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.discharge_time).HasComment("出院时间");
            entity.Property(e => e.link_id)
                .IsRequired()
                .HasComment("关联号");
            entity.Property(e => e.measurement_unit).HasComment("测量单位");
            entity.Property(e => e.project_code)
                .IsRequired()
                .HasComment("项目代号");
            entity.Property(e => e.project_name)
                .IsRequired()
                .HasComment("项目名称");
            entity.Property(e => e.project_value_char).HasComment("项目值(字符)");
            entity.Property(e => e.project_value_num).HasComment("项目值(数值)");
            entity.Property(e => e.record_id)
                .IsRequired()
                .HasComment("住院号/门诊号");
            entity.Property(e => e.record_time)
                .HasComment("记录时间")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.unique_id)
                .IsRequired()
                .HasComment("唯一号");

            entity.HasOne(d => d.link).WithMany()
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_nursing_record_link_id");
        });

        modelBuilder.Entity<operation_info>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable(tb => tb.HasComment("手术操作表"));

            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.main_operation_anesthesia_level).HasComment("主要手术操作麻醉分级");
            entity.Property(e => e.main_operation_anesthesia_method).HasComment("主要手术操作麻醉方式");
            entity.Property(e => e.main_operation_anesthesiologist).HasComment("主要手术操作麻醉医师");
            entity.Property(e => e.main_operation_code).HasComment("主要手术操作编码");
            entity.Property(e => e.main_operation_date).HasComment("主要手术操作日期");
            entity.Property(e => e.main_operation_duration).HasComment("主要手术操作持续时间");
            entity.Property(e => e.main_operation_first_assistant).HasComment("主要手术操作Ⅰ助");
            entity.Property(e => e.main_operation_level).HasComment("主要手术操作级别");
            entity.Property(e => e.main_operation_name).HasComment("主要手术操作名称");
            entity.Property(e => e.main_operation_second_assistant).HasComment("主要手术操作Ⅱ助");
            entity.Property(e => e.main_operation_surgeon).HasComment("主要手术操作术者");
            entity.Property(e => e.main_operation_wound_healing_grade).HasComment("主要手术操作切口愈合等级");
            entity.Property(e => e.other_operation_anesthesia_levels).HasComment("其他手术操作麻醉分级数组");
            entity.Property(e => e.other_operation_anesthesia_methods).HasComment("其他手术操作麻醉方式数组");
            entity.Property(e => e.other_operation_anesthesiologists).HasComment("其他手术操作麻醉医师数组");
            entity.Property(e => e.other_operation_codes).HasComment("其他手术操作编码数组");
            entity.Property(e => e.other_operation_dates).HasComment("其他手术操作日期数组");
            entity.Property(e => e.other_operation_durations).HasComment("其他手术操作持续时间数组");
            entity.Property(e => e.other_operation_first_assistants).HasComment("其他手术操作Ⅰ助数组");
            entity.Property(e => e.other_operation_levels).HasComment("其他手术操作级别数组");
            entity.Property(e => e.other_operation_names).HasComment("其他手术操作名称数组");
            entity.Property(e => e.other_operation_second_assistants).HasComment("其他手术操作Ⅱ助数组");
            entity.Property(e => e.other_operation_surgeons).HasComment("其他手术操作术者数组");
            entity.Property(e => e.other_operation_wound_healing_grades).HasComment("其他手术操作切口愈合等级数组");
            entity.Property(e => e.record_id).HasComment("病案号/住院号/门诊号");
            entity.Property(e => e.unique_id).HasComment("唯一号");

            entity.HasOne(d => d.link).WithMany()
                .HasForeignKey(d => d.link_id)
                .HasConstraintName("fk_operation_info_link_id");
        });

        modelBuilder.Entity<patient_base>(entity =>
        {
            entity.HasKey(e => e.link_id).HasName("pk_patient_base");

            entity.ToTable(tb => tb.HasComment("患者基本信息表"));

            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.age).HasComment("年龄");
            entity.Property(e => e.birth_date).HasComment("出生日期");
            entity.Property(e => e.gender)
                .IsRequired()
                .HasComment("性别");
            entity.Property(e => e.patient_name)
                .IsRequired()
                .HasComment("姓名");
            entity.Property(e => e.phone_number).HasComment("联系方式");
            entity.Property(e => e.record_id).IsRequired();
            entity.Property(e => e.sid_card).HasComment("身份证号码");
            entity.Property(e => e.unique_id)
                .IsRequired()
                .HasComment("唯一号");
        });

        modelBuilder.Entity<patient_basic_info>(entity =>
        {
            entity.HasKey(e => e.unique_id).HasName("pk_patient_basic_info");

            entity.ToTable(tb => tb.HasComment("患者基本信息表"));

            entity.Property(e => e.unique_id).HasComment("唯一号");
            entity.Property(e => e.abo_blood_type).HasComment("ABO血型");
            entity.Property(e => e.age).HasComment("年龄");
            entity.Property(e => e.allergic_drug_name).HasComment("过敏药物名称");
            entity.Property(e => e.birth_address).HasComment("出生地址");
            entity.Property(e => e.birth_date).HasComment("出生日期");
            entity.Property(e => e.contact_address).HasComment("联系人地址");
            entity.Property(e => e.contact_name).HasComment("联系人姓名");
            entity.Property(e => e.contact_phone).HasComment("联系人电话");
            entity.Property(e => e.contact_relationship).HasComment("联系人关系");
            entity.Property(e => e.current_address).HasComment("现住址");
            entity.Property(e => e.current_address_phone).HasComment("现住址电话");
            entity.Property(e => e.current_address_postal_code).HasComment("现住址邮政编码");
            entity.Property(e => e.ethnicity).HasComment("民族");
            entity.Property(e => e.gender).HasComment("性别");
            entity.Property(e => e.has_drug_allergy).HasComment("有无药物过敏");
            entity.Property(e => e.hbsag).HasComment("HBsAg");
            entity.Property(e => e.hcv_ab).HasComment("HCV-Ab");
            entity.Property(e => e.health_card_number).HasComment("健康卡号");
            entity.Property(e => e.hiv_ab).HasComment("HIV-Ab");
            entity.Property(e => e.household_address).HasComment("户口地址");
            entity.Property(e => e.household_postal_code).HasComment("户口地址邮政编码");
            entity.Property(e => e.id_number).HasComment("身份证号");
            entity.Property(e => e.id_type).HasComment("证件类别");
            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.marital_status).HasComment("婚姻");
            entity.Property(e => e.name).HasComment("姓名");
            entity.Property(e => e.nationality).HasComment("国籍");
            entity.Property(e => e.native_province).HasComment("籍贯省（自治区、直辖市）");
            entity.Property(e => e.occupation).HasComment("职业");
            entity.Property(e => e.record_id).HasComment("病案号/住院号/门诊号");
            entity.Property(e => e.rh_blood_type).HasComment("Rh血型");
            entity.Property(e => e.work_unit_address).HasComment("工作单位及地址");
            entity.Property(e => e.work_unit_phone).HasComment("工作单位电话");
            entity.Property(e => e.work_unit_postal_code).HasComment("工作单位邮政编码");

            entity.HasOne(d => d.link).WithMany(p => p.patient_basic_info)
                .HasForeignKey(d => d.link_id)
                .HasConstraintName("fk_patient_basic_info_link_id");
        });

        modelBuilder.Entity<patient_blood_gas_results>(entity =>
        {
            entity.HasNoKey();

            entity.HasIndex(e => new { e.patient_serial_number, e.blood_gas_id, e.test_time }, "patient_blood_gas_results2_patient_serial_number_blood_gas__key").IsUnique();

            entity.Property(e => e.AaDpO2_T).HasColumnName("AaDpO2,T");
            entity.Property(e => e.Ca_7_4_).HasColumnName("Ca(7.4)");
            entity.Property(e => e.Ca__).HasColumnName("Ca++");
            entity.Property(e => e.Cl_).HasColumnName("Cl-");
            entity.Property(e => e.HCO3_).HasColumnName("HCO3-");
            entity.Property(e => e.K_).HasColumnName("K+");
            entity.Property(e => e.Na_).HasColumnName("Na+");
            entity.Property(e => e.blood_gas_id).IsRequired();
            entity.Property(e => e.cBase_B__c).HasColumnName("cBase(B),c");
            entity.Property(e => e.cBase_Ecf__c).HasColumnName("cBase(Ecf),c");
            entity.Property(e => e.cBase_Ecf_ox_).HasColumnName("cBase(Ecf,ox)");
            entity.Property(e => e.cHCO3__P_st__c).HasColumnName("cHCO3-(P,st),c");
            entity.Property(e => e.created_at)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.p50_act_).HasColumnName("p50(act)");
            entity.Property(e => e.pCO2_T_).HasColumnName("pCO2(T)");
            entity.Property(e => e.pH_T_).HasColumnName("pH(T)");
            entity.Property(e => e.pO2_T_).HasColumnName("pO2(T)");
            entity.Property(e => e.pO2_a__FIO2).HasColumnName("pO2(a)/FIO2");
            entity.Property(e => e.patient_serial_number).IsRequired();
            entity.Property(e => e.tCO2_B_).HasColumnName("tCO2(B)");
            entity.Property(e => e.tCO2_P_).HasColumnName("tCO2(P)");
            entity.Property(e => e.test_time).HasColumnType("timestamp without time zone");
        });

        modelBuilder.Entity<patient_diagnosis>(entity =>
        {
            entity.HasKey(e => new { e.link_id, e.record_id, e.admission_time, e.diagnosis_type, e.diagnosis_seq }).HasName("pk_patient_diagnosis");

            entity.ToTable(tb => tb.HasComment("患者诊断表"));

            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.record_id).HasComment("住院号/门诊号");
            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.diagnosis_type).HasComment("诊断类型");
            entity.Property(e => e.diagnosis_seq).HasComment("诊断序号");
            entity.Property(e => e.condition_on_admission).HasComment("入院病情");
            entity.Property(e => e.condition_on_discharge).HasComment("出院情况");
            entity.Property(e => e.diagnosis_code).HasComment("诊断编码");
            entity.Property(e => e.diagnosis_name).HasComment("诊断名称");
            entity.Property(e => e.pathology_number).HasComment("病理号");

            entity.HasOne(d => d.link).WithMany(p => p.patient_diagnosis)
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_patient_diagnosis_link_id");
        });

        modelBuilder.Entity<patient_extractions>(entity =>
        {
            entity.HasKey(e => new { e.patient_unique_id, e.task_code }).HasName("patient_extractions_pkey");

            entity.ToTable("patient_extractions", "etl", tb => tb.HasComment("患者提取结果表，存储每个患者每个任务的提取结果和LLM思维链"));

            entity.HasIndex(e => e.created_time, "idx_patient_extractions_created_time");

            entity.HasIndex(e => e.patient_unique_id, "idx_patient_extractions_patient_id");

            entity.HasIndex(e => e.task_code, "idx_patient_extractions_task_code");

            entity.Property(e => e.patient_unique_id).HasComment("患者唯一号，关联patient_basic_info.unique_id");
            entity.Property(e => e.task_code).HasComment("任务编号，关联ards_data_etl_rules.task_code");
            entity.Property(e => e.created_time)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .HasComment("记录创建时间")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.extraction_result).HasComment("数据提取结果");
            entity.Property(e => e.llm_reasoning_chain).HasComment("LLM推理思维链，用于质量检查");

            entity.HasOne(d => d.patient_unique).WithMany(p => p.patient_extractions)
                .HasForeignKey(d => d.patient_unique_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_patient_extractions_patient");

            entity.HasOne(d => d.task_codeNavigation).WithMany(p => p.patient_extractions)
                .HasForeignKey(d => d.task_code)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_patient_extractions_task");
        });

        modelBuilder.Entity<patient_progress_record>(entity =>
        {
            entity.HasKey(e => new { e.link_id, e.record_id, e.admission_time, e.discharge_time, e.record_date, e.record_name }).HasName("pk_patient_progress_record");

            entity.ToTable(tb => tb.HasComment("患者病程记录表"));

            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.record_id).HasComment("住院号/门诊号");
            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.discharge_time).HasComment("出院时间");
            entity.Property(e => e.record_date)
                .HasComment("日期")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.record_name).HasComment("记录名称");
            entity.Property(e => e.age).HasComment("年龄");
            entity.Property(e => e.birth_date).HasComment("出生日期");
            entity.Property(e => e.gender)
                .IsRequired()
                .HasComment("性别");
            entity.Property(e => e.patient_name)
                .IsRequired()
                .HasComment("姓名");
            entity.Property(e => e.progress_notes).HasComment("病程记录");
            entity.Property(e => e.unique_id)
                .IsRequired()
                .HasComment("唯一号");

            entity.HasOne(d => d.link).WithMany(p => p.patient_progress_record)
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_patient_progress_record_link_id");
        });

        modelBuilder.Entity<respiratory_support>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable(tb => tb.HasComment("呼吸支持"));

            entity.HasIndex(e => e.unique_id, "idx_respiratory_support_unique_id");

            entity.Property(e => e.dtime)
                .HasComment("时间")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.name)
                .IsRequired()
                .HasComment("名称");
            entity.Property(e => e.unique_id)
                .IsRequired()
                .HasComment("唯一号");
            entity.Property(e => e.value)
                .IsRequired()
                .HasComment("值");
        });

        modelBuilder.Entity<surgical_operation>(entity =>
        {
            entity.HasKey(e => new { e.link_id, e.record_id, e.admission_time, e.operation_type, e.operation_seq }).HasName("pk_surgical_operation");

            entity.ToTable(tb => tb.HasComment("手术操作表"));

            entity.Property(e => e.link_id).HasComment("关联号");
            entity.Property(e => e.record_id).HasComment("住院号/门诊号");
            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.operation_type).HasComment("手术类型");
            entity.Property(e => e.operation_seq).HasComment("手术序号");
            entity.Property(e => e.anesthesia_level).HasComment("手术操作麻醉分级");
            entity.Property(e => e.anesthesia_method).HasComment("手术操作麻醉方式");
            entity.Property(e => e.anesthesiologist).HasComment("手术操作麻醉医师");
            entity.Property(e => e.first_assistant).HasComment("手术操作Ⅰ助");
            entity.Property(e => e.incision_healing_grade).HasComment("手术操作切口愈合等级");
            entity.Property(e => e.operation_code).HasComment("手术操作编码");
            entity.Property(e => e.operation_date).HasComment("手术操作日期");
            entity.Property(e => e.operation_duration).HasComment("手术操作持续时间");
            entity.Property(e => e.operation_level).HasComment("手术操作级别");
            entity.Property(e => e.operation_name).HasComment("手术操作名称");
            entity.Property(e => e.second_assistant).HasComment("手术操作Ⅱ助");
            entity.Property(e => e.surgeon).HasComment("手术操作术者");

            entity.HasOne(d => d.link).WithMany(p => p.surgical_operation)
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_surgical_operation_link_id");
        });

        modelBuilder.Entity<surgical_progress_record>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable(tb => tb.HasComment("患者手术病程记录表"));

            entity.Property(e => e.admission_time).HasComment("入院时间");
            entity.Property(e => e.age).HasComment("年龄");
            entity.Property(e => e.birth_date).HasComment("出生日期");
            entity.Property(e => e.discharge_time).HasComment("出院时间");
            entity.Property(e => e.duration).HasComment("手术时长");
            entity.Property(e => e.end_time).HasComment("结束时间");
            entity.Property(e => e.first_assistant).HasComment("第一助手");
            entity.Property(e => e.gender)
                .IsRequired()
                .HasComment("性别");
            entity.Property(e => e.link_id)
                .IsRequired()
                .HasComment("关联号");
            entity.Property(e => e.patient_name)
                .IsRequired()
                .HasComment("姓名");
            entity.Property(e => e.record_id)
                .IsRequired()
                .HasComment("住院号/门诊号");
            entity.Property(e => e.second_assistant).HasComment("第二助手");
            entity.Property(e => e.start_time).HasComment("开始时间");
            entity.Property(e => e.surgeon).HasComment("手术医生");
            entity.Property(e => e.surgery_date).HasComment("手术日期");
            entity.Property(e => e.surgery_name).HasComment("手术名称");
            entity.Property(e => e.surgery_notes).HasComment("手术记录");
            entity.Property(e => e.unique_id).HasComment("唯一号");

            entity.HasOne(d => d.link).WithMany()
                .HasForeignKey(d => d.link_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_surgical_progress_record_link_id");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}