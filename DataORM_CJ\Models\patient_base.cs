﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 患者基本信息表
/// </summary>
public partial class patient_base
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    public string record_id { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string patient_name { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int age { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string gender { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateOnly birth_date { get; set; }

    /// <summary>
    /// 身份证号码
    /// </summary>
    public string sid_card { get; set; }

    /// <summary>
    /// 联系方式
    /// </summary>
    public string phone_number { get; set; }

    public virtual ICollection<blood_transfusion> blood_transfusion { get; set; } = new List<blood_transfusion>();

    public virtual ICollection<electronic_medical_record> electronic_medical_record { get; set; } = new List<electronic_medical_record>();

    public virtual ICollection<icu_record> icu_record { get; set; } = new List<icu_record>();

    public virtual ICollection<medical_cost_detail> medical_cost_detail { get; set; } = new List<medical_cost_detail>();

    public virtual ICollection<medical_record> medical_record { get; set; } = new List<medical_record>();

    public virtual ICollection<medical_record_front_page> medical_record_front_page { get; set; } = new List<medical_record_front_page>();

    public virtual ICollection<nursing_level> nursing_level { get; set; } = new List<nursing_level>();

    public virtual ICollection<patient_basic_info> patient_basic_info { get; set; } = new List<patient_basic_info>();

    public virtual ICollection<patient_diagnosis> patient_diagnosis { get; set; } = new List<patient_diagnosis>();

    public virtual ICollection<patient_progress_record> patient_progress_record { get; set; } = new List<patient_progress_record>();

    public virtual ICollection<surgical_operation> surgical_operation { get; set; } = new List<surgical_operation>();
}