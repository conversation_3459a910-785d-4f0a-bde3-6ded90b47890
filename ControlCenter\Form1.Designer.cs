﻿namespace ControlCenter
{
    partial class Form1
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.文件ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.编辑ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.帮助ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
            this.splitContainerMain = new System.Windows.Forms.SplitContainer();
            this.splitContainerTop = new System.Windows.Forms.SplitContainer();
            this.patientListControl1 = new ControlCenter.PatientListControl();
            this.etlRulesControl1 = new ControlCenter.EtlRulesControl();
            this.txtSelectedPatientsDetails = new System.Windows.Forms.TextBox();
            this.menuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerMain)).BeginInit();
            this.splitContainerMain.Panel1.SuspendLayout();
            this.splitContainerMain.Panel2.SuspendLayout();
            this.splitContainerMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerTop)).BeginInit();
            this.splitContainerTop.Panel1.SuspendLayout();
            this.splitContainerTop.Panel2.SuspendLayout();
            this.splitContainerTop.SuspendLayout();
            this.SuspendLayout();
            //
            // menuStrip1
            //
            this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.文件ToolStripMenuItem,
            this.编辑ToolStripMenuItem,
            this.帮助ToolStripMenuItem});
            this.menuStrip1.Location = new System.Drawing.Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Size = new System.Drawing.Size(800, 24);
            this.menuStrip1.TabIndex = 0;
            this.menuStrip1.Text = "menuStrip1";
            //
            // 文件ToolStripMenuItem
            //
            this.文件ToolStripMenuItem.Name = "文件ToolStripMenuItem";
            this.文件ToolStripMenuItem.Size = new System.Drawing.Size(44, 20);
            this.文件ToolStripMenuItem.Text = "文件";
            //
            // 编辑ToolStripMenuItem
            //
            this.编辑ToolStripMenuItem.Name = "编辑ToolStripMenuItem";
            this.编辑ToolStripMenuItem.Size = new System.Drawing.Size(44, 20);
            this.编辑ToolStripMenuItem.Text = "编辑";
            //
            // 帮助ToolStripMenuItem
            //
            this.帮助ToolStripMenuItem.Name = "帮助ToolStripMenuItem";
            this.帮助ToolStripMenuItem.Size = new System.Drawing.Size(44, 20);
            this.帮助ToolStripMenuItem.Text = "帮助";
            //
            // statusStrip1
            //
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabel1});
            this.statusStrip1.Location = new System.Drawing.Point(0, 428);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Size = new System.Drawing.Size(800, 22);
            this.statusStrip1.TabIndex = 1;
            this.statusStrip1.Text = "statusStrip1";
            //
            // toolStripStatusLabel1
            //
            this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            this.toolStripStatusLabel1.Size = new System.Drawing.Size(32, 17);
            this.toolStripStatusLabel1.Text = "就绪";
            //
            // splitContainerMain
            //
            this.splitContainerMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerMain.Location = new System.Drawing.Point(0, 24);
            this.splitContainerMain.Name = "splitContainerMain";
            this.splitContainerMain.Orientation = System.Windows.Forms.Orientation.Horizontal;
            //
            // splitContainerMain.Panel1
            //
            this.splitContainerMain.Panel1.Controls.Add(this.splitContainerTop);
            //
            // splitContainerMain.Panel2
            //
            this.splitContainerMain.Panel2.Controls.Add(this.txtSelectedPatientsDetails);
            this.splitContainerMain.Size = new System.Drawing.Size(800, 404);
            this.splitContainerMain.SplitterDistance = 202;
            this.splitContainerMain.TabIndex = 2;
            //
            // splitContainerTop
            //
            this.splitContainerTop.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerTop.Location = new System.Drawing.Point(0, 0);
            this.splitContainerTop.Name = "splitContainerTop";
            //
            // splitContainerTop.Panel1
            //
            this.splitContainerTop.Panel1.Controls.Add(this.patientListControl1);
            //
            // splitContainerTop.Panel2
            //
            this.splitContainerTop.Panel2.Controls.Add(this.etlRulesControl1);
            this.splitContainerTop.Size = new System.Drawing.Size(800, 202);
            this.splitContainerTop.SplitterDistance = 400;
            this.splitContainerTop.TabIndex = 0;
            //
            // patientListControl1
            //
            this.patientListControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.patientListControl1.Location = new System.Drawing.Point(0, 0);
            this.patientListControl1.Name = "patientListControl1";
            this.patientListControl1.Size = new System.Drawing.Size(400, 202);
            this.patientListControl1.TabIndex = 0;
            //
            // etlRulesControl1
            //
            this.etlRulesControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.etlRulesControl1.Location = new System.Drawing.Point(0, 0);
            this.etlRulesControl1.Name = "etlRulesControl1";
            this.etlRulesControl1.Size = new System.Drawing.Size(396, 202);
            this.etlRulesControl1.TabIndex = 0;
            //
            // txtSelectedPatientsDetails
            //
            this.txtSelectedPatientsDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtSelectedPatientsDetails.Location = new System.Drawing.Point(0, 0);
            this.txtSelectedPatientsDetails.Multiline = true;
            this.txtSelectedPatientsDetails.Name = "txtSelectedPatientsDetails";
            this.txtSelectedPatientsDetails.ReadOnly = true;
            this.txtSelectedPatientsDetails.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtSelectedPatientsDetails.Size = new System.Drawing.Size(800, 198);
            this.txtSelectedPatientsDetails.TabIndex = 0;
            this.txtSelectedPatientsDetails.Font = new System.Drawing.Font("Consolas", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            //
            // Form1
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.splitContainerMain);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.menuStrip1);
            this.MainMenuStrip = this.menuStrip1;
            this.Name = "Form1";
            this.Text = "医疗数据ETL控制中心";
            this.Load += new System.EventHandler(this.Form1_Load);
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.splitContainerMain.Panel1.ResumeLayout(false);
            this.splitContainerMain.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerMain)).EndInit();
            this.splitContainerMain.ResumeLayout(false);
            this.splitContainerTop.Panel1.ResumeLayout(false);
            this.splitContainerTop.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerTop)).EndInit();
            this.splitContainerTop.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private System.Windows.Forms.MenuStrip menuStrip1;
        private System.Windows.Forms.ToolStripMenuItem 文件ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 编辑ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 帮助ToolStripMenuItem;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.SplitContainer splitContainerMain;
        private System.Windows.Forms.SplitContainer splitContainerTop;
        private PatientListControl patientListControl1;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel1;
        private EtlRulesControl etlRulesControl1;
        private System.Windows.Forms.TextBox txtSelectedPatientsDetails;
    }
}
