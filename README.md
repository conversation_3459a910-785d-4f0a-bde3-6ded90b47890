# 医疗数据ETL控制中心

## 项目概述

这是一个医疗数据ETL（Extract, Transform, Load）控制中心，用于管理和查看医疗数据。采用组件化设计，功能模块化程度高，易于维护和扩展。

## 功能特性

### 患者列表管理组件 (PatientListControl)
- **独立组件**：可重用的患者列表用户控件
- **数据展示**：显示患者基本信息列表，支持4000+条记录
- **列选择**：通过下拉菜单动态选择要显示的列
- **中文界面**：所有列标题和界面文字都使用中文显示
- **异步加载**：异步加载患者数据，提供状态反馈
- **事件驱动**：通过事件机制与主窗体通信

### 默认显示列
- 姓名
- 性别  
- 年龄
- 出生日期
- 身份证号
- 联系电话

### 可选显示列
用户可以通过"显示列"下拉菜单选择显示以下任意列：
- 唯一号、关联号、病案号
- 健康卡号、国籍、婚姻状况
- 职业、民族、证件类别
- 各种地址信息（出生地、户口地址、现住址、工作单位等）
- 联系人信息
- 血型信息（ABO、Rh）
- 过敏信息等

## 技术架构

- **前端**：WinForms (.NET 8.0)
- **数据访问**：Entity Framework Core 8.0
- **数据库**：PostgreSQL
- **架构模式**：组件化三层架构（UI层、业务层、数据访问层）
- **设计模式**：事件驱动、控件组合

## 组件设计

### PatientListControl 用户控件

该控件是一个独立的患者列表组件，具有以下特点：

#### 公共接口
```csharp
// 初始化数据库连接并加载数据
public async Task InitializeAsync(string? connectionString = null)

// 刷新患者数据
public async Task RefreshAsync()

// 获取当前选中的患者
public patient_basic_info? GetSelectedPatient()

// 患者选择事件
public event EventHandler<patient_basic_info>? PatientSelected;

// 状态变化事件
public event EventHandler<string>? StatusChanged;
```

#### 使用示例
```csharp
// 在窗体中使用
patientListControl1.StatusChanged += (sender, status) => 
{
    toolStripStatusLabel1.Text = status;
};

patientListControl1.PatientSelected += (sender, patient) => 
{
    // 处理患者选择事件
    labelRight.Text = $"选中患者：{patient.name}";
};

await patientListControl1.InitializeAsync();
```

## 配置说明

### 数据库连接配置

编辑 `ControlCenter/appsettings.json` 文件：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=china;Username=postgres;Password=******;"
  }
}
```

请根据您的实际数据库环境修改以下参数：
- `Host`：数据库服务器地址
- `Port`：数据库端口（默认5432）
- `Database`：数据库名称
- `Username`：数据库用户名
- `Password`：数据库密码

## 运行要求

1. **.NET 8.0 Runtime**
2. **PostgreSQL数据库**，包含 `patient_basic_info` 表
3. **网络连接**到数据库服务器

## 使用说明

1. **启动应用**：运行 `ControlCenter.exe`
2. **查看患者列表**：应用启动后自动加载患者数据
3. **选择显示列**：点击工具栏上的"显示列 ▼"按钮，勾选要显示的列
4. **状态查看**：底部状态栏显示数据加载状态和记录数量
5. **患者选择**：点击患者行，右侧区域会显示患者基本信息

## 项目结构

```
MedDataETL.AI/
├── ControlCenter/              # 主应用程序（WinForms）
│   ├── Form1.cs               # 主窗体逻辑
│   ├── Form1.Designer.cs      # 主窗体设计
│   ├── PatientListControl.cs  # 患者列表用户控件
│   ├── PatientListControl.Designer.cs # 患者列表控件设计
│   └── appsettings.json       # 配置文件
├── DataORM_CJ/                # 数据访问层（EF Core）
│   └── Models/                # 数据模型
└── BaseLib/                   # 基础类库
```

## 开发指南

### 创建新的用户控件

1. **继承UserControl**：所有自定义控件都应继承自UserControl
2. **实现IDisposable**：正确释放资源，特别是数据库连接
3. **使用事件机制**：通过事件与其他组件通信
4. **提供公共接口**：定义清晰的公共方法和属性

### 添加新的显示列

1. 在 `PatientListControl.cs` 的 `GetDisplayName()` 方法中添加新的中文名称映射
2. 在 `IsDefaultVisible()` 方法中配置是否默认显示

### 扩展功能

当前实现遵循极简原则，如需添加搜索、分页、详情查看等功能，可以基于现有架构进行扩展：

- **搜索功能**：在PatientListControl中添加搜索方法
- **分页功能**：扩展数据加载方法支持分页
- **详情窗口**：创建新的UserControl显示患者详细信息
- **数据编辑**：添加患者信息编辑功能

## 技术优势

1. **模块化设计**：PatientListControl可以在其他窗体中重用
2. **松耦合**：通过事件机制实现组件间通信
3. **易于测试**：每个组件可以独立测试
4. **维护性强**：功能职责清晰，修改影响范围小
5. **扩展性好**：可以轻松添加新的功能组件

## 最佳实践

1. **资源管理**：及时释放数据库连接和其他资源
2. **异步编程**：使用async/await处理耗时操作
3. **错误处理**：提供友好的错误消息和状态反馈
4. **配置管理**：使用配置文件管理数据库连接等设置
5. **代码复用**：通过组件化设计提高代码复用率 