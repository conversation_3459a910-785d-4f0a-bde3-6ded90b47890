﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 患者提取结果表，存储每个患者每个任务的提取结果和LLM思维链
/// </summary>
public partial class patient_extractions
{
    /// <summary>
    /// 患者唯一号，关联patient_basic_info.unique_id
    /// </summary>
    public string patient_unique_id { get; set; }

    /// <summary>
    /// 任务编号，关联ards_data_etl_rules.task_code
    /// </summary>
    public string task_code { get; set; }

    /// <summary>
    /// 数据提取结果
    /// </summary>
    public string extraction_result { get; set; }

    /// <summary>
    /// LLM推理思维链，用于质量检查
    /// </summary>
    public string llm_reasoning_chain { get; set; }

    /// <summary>
    /// 记录创建时间
    /// </summary>
    public DateTime? created_time { get; set; }

    public virtual patient_basic_info patient_unique { get; set; }

    public virtual ards_data_etl_rules task_codeNavigation { get; set; }
}