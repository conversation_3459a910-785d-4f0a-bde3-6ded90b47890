﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 实验室检查数据表
/// </summary>
public partial class laboratory_test_record
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    /// <summary>
    /// 住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string patient_name { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int age { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string gender { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateOnly birth_date { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    public DateOnly admission_time { get; set; }

    /// <summary>
    /// 出院时间
    /// </summary>
    public DateOnly? discharge_time { get; set; }

    /// <summary>
    /// 报告日期
    /// </summary>
    public DateTime report_date { get; set; }

    /// <summary>
    /// 检验项目
    /// </summary>
    public string test_item { get; set; }

    /// <summary>
    /// 检验指标名
    /// </summary>
    public string indicator_name { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string english_name { get; set; }

    /// <summary>
    /// 检验指标值
    /// </summary>
    public string indicator_value { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string unit { get; set; }

    /// <summary>
    /// 参考范围
    /// </summary>
    public string reference_range { get; set; }

    public virtual patient_base link { get; set; }
}