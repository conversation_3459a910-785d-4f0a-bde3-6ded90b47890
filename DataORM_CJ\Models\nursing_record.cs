﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 患者护理记录表
/// </summary>
public partial class nursing_record
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    /// <summary>
    /// 住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    public DateOnly admission_time { get; set; }

    /// <summary>
    /// 出院时间
    /// </summary>
    public DateOnly discharge_time { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string project_name { get; set; }

    /// <summary>
    /// 项目代号
    /// </summary>
    public string project_code { get; set; }

    /// <summary>
    /// 记录时间
    /// </summary>
    public DateTime record_time { get; set; }

    /// <summary>
    /// 项目值(字符)
    /// </summary>
    public string project_value_char { get; set; }

    /// <summary>
    /// 项目值(数值)
    /// </summary>
    public string project_value_num { get; set; }

    /// <summary>
    /// 测量单位
    /// </summary>
    public string measurement_unit { get; set; }

    public virtual patient_base link { get; set; }
}