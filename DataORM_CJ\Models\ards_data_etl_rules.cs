﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 中日友好医院ARDS数据清洗规则表
/// </summary>
public partial class ards_data_etl_rules
{
    /// <summary>
    /// 任务编号，如ETL_CJ_101
    /// </summary>
    public string task_code { get; set; }

    /// <summary>
    /// 是否提取标识，如&quot;本次需提取&quot;
    /// </summary>
    public string is_extract { get; set; }

    /// <summary>
    /// 数据字典名称
    /// </summary>
    public string dict_name { get; set; }

    /// <summary>
    /// 数据提取位置说明
    /// </summary>
    public string extract_location { get; set; }

    /// <summary>
    /// 详细备注和提取规则
    /// </summary>
    public string remark { get; set; }

    /// <summary>
    /// LLM提取prompt
    /// </summary>
    public string prompt { get; set; }

    /// <summary>
    /// 数据需求
    /// </summary>
    public string sql_script { get; set; }

    public virtual ICollection<patient_extractions> patient_extractions { get; set; } = new List<patient_extractions>();
}