﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 影像学检查数据表
/// </summary>
public partial class imaging_examination_record
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    /// <summary>
    /// 住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string patient_name { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int age { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string gender { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateOnly birth_date { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    public DateOnly admission_time { get; set; }

    /// <summary>
    /// 出院时间
    /// </summary>
    public DateOnly? discharge_time { get; set; }

    /// <summary>
    /// 检查号
    /// </summary>
    public string examination_no { get; set; }

    /// <summary>
    /// 检查设备类型
    /// </summary>
    public string device_type { get; set; }

    /// <summary>
    /// 检查项目
    /// </summary>
    public string examination_item { get; set; }

    /// <summary>
    /// 检查部位
    /// </summary>
    public string examination_part { get; set; }

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime examination_time { get; set; }

    /// <summary>
    /// 影像所见
    /// </summary>
    public string imaging_finding { get; set; }

    /// <summary>
    /// 检查结论
    /// </summary>
    public string conclusion { get; set; }

    /// <summary>
    /// 送检科室名称
    /// </summary>
    public string referring_department { get; set; }

    public virtual patient_base link { get; set; }
}