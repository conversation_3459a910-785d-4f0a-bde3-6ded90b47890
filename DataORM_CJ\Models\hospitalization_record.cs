﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 住院记录表
/// </summary>
public partial class hospitalization_record
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    /// <summary>
    /// 病案号/住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 组织机构代码
    /// </summary>
    public string organization_code { get; set; }

    /// <summary>
    /// 医疗机构名称
    /// </summary>
    public string medical_institution_name { get; set; }

    /// <summary>
    /// 住院次数
    /// </summary>
    public string hospitalization_count { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    public string admission_time { get; set; }

    /// <summary>
    /// 出院时间
    /// </summary>
    public string discharge_time { get; set; }

    /// <summary>
    /// 医疗付费方式
    /// </summary>
    public string payment_method { get; set; }

    /// <summary>
    /// 是否为日间手术
    /// </summary>
    public string is_day_surgery { get; set; }

    /// <summary>
    /// 入院途径
    /// </summary>
    public string admission_path { get; set; }

    /// <summary>
    /// 入院科别
    /// </summary>
    public string admission_department { get; set; }

    /// <summary>
    /// 入院病房
    /// </summary>
    public string admission_ward { get; set; }

    /// <summary>
    /// 转科科别
    /// </summary>
    public string transfer_department { get; set; }

    /// <summary>
    /// 出院科别
    /// </summary>
    public string discharge_department { get; set; }

    /// <summary>
    /// 出院病房
    /// </summary>
    public string discharge_ward { get; set; }

    /// <summary>
    /// 实际住院（天）
    /// </summary>
    public string actual_hospitalization_days { get; set; }

    /// <summary>
    /// 是否有出院31日内再住院计划
    /// </summary>
    public string has_rehospitalization_plan { get; set; }

    /// <summary>
    /// 出院31天再住院计划目的
    /// </summary>
    public string rehospitalization_plan_purpose { get; set; }

    /// <summary>
    /// 离院方式
    /// </summary>
    public string discharge_method { get; set; }

    /// <summary>
    /// 医嘱转院、转社区卫生服务机构/乡镇卫生院名称
    /// </summary>
    public string transfer_institution_name { get; set; }

    /// <summary>
    /// 入院时情况
    /// </summary>
    public string admission_condition { get; set; }

    /// <summary>
    /// 门（急）诊诊断编码
    /// </summary>
    public string emergency_diagnosis_code { get; set; }

    /// <summary>
    /// 门（急）诊诊断名称
    /// </summary>
    public string emergency_diagnosis_name { get; set; }

    /// <summary>
    /// 死亡患者尸检
    /// </summary>
    public string death_patient_autopsy { get; set; }

    /// <summary>
    /// 年龄不足1周岁的年龄（天）
    /// </summary>
    public string age_under_one_week_days { get; set; }

    /// <summary>
    /// 新生儿出生体重（克）数组，最多5个
    /// </summary>
    public List<string> newborn_birth_weights { get; set; }

    /// <summary>
    /// 新生儿入院体重（克）
    /// </summary>
    public string newborn_admission_weight { get; set; }

    /// <summary>
    /// 颅脑损伤患者入院前昏迷时间（天）
    /// </summary>
    public string craniocerebral_injury_before_coma_days { get; set; }

    /// <summary>
    /// 颅脑损伤患者入院前昏迷时间（小时）
    /// </summary>
    public string craniocerebral_injury_before_coma_hours { get; set; }

    /// <summary>
    /// 颅脑损伤患者入院前昏迷时间（分钟）
    /// </summary>
    public string craniocerebral_injury_before_coma_minutes { get; set; }

    /// <summary>
    /// 颅脑损伤患者入院后昏迷时间（天）
    /// </summary>
    public string craniocerebral_injury_after_coma_days { get; set; }

    /// <summary>
    /// 颅脑损伤患者入院后昏迷时间（小时）
    /// </summary>
    public string craniocerebral_injury_after_coma_hours { get; set; }

    /// <summary>
    /// 颅脑损伤患者入院后昏迷时间（分钟）
    /// </summary>
    public string craniocerebral_injury_after_coma_minutes { get; set; }

    /// <summary>
    /// 特级护理天数
    /// </summary>
    public string special_care_days { get; set; }

    /// <summary>
    /// 一级护理天数
    /// </summary>
    public string first_level_care_days { get; set; }

    /// <summary>
    /// 二级护理天数
    /// </summary>
    public string second_level_care_days { get; set; }

    /// <summary>
    /// 三级护理天数
    /// </summary>
    public string third_level_care_days { get; set; }

    /// <summary>
    /// 输血反应
    /// </summary>
    public string blood_transfusion_reaction { get; set; }

    /// <summary>
    /// 红细胞
    /// </summary>
    public string red_blood_cells { get; set; }

    /// <summary>
    /// 血小板
    /// </summary>
    public string platelets { get; set; }

    /// <summary>
    /// 血浆
    /// </summary>
    public string plasma { get; set; }

    /// <summary>
    /// 全血
    /// </summary>
    public string whole_blood { get; set; }

    /// <summary>
    /// 自体血回输
    /// </summary>
    public string autologous_blood_transfusion { get; set; }

    /// <summary>
    /// 有创呼吸机使用时间
    /// </summary>
    public string invasive_ventilator_time { get; set; }

    /// <summary>
    /// 重症监护室名称数组
    /// </summary>
    public List<string> icu_names { get; set; }

    /// <summary>
    /// 重症监护室进入时间数组
    /// </summary>
    public List<string> icu_entry_times { get; set; }

    /// <summary>
    /// 重症监护室退出时间数组
    /// </summary>
    public List<string> icu_exit_times { get; set; }

    /// <summary>
    /// 科主任执行编号
    /// </summary>
    public string department_director_execution_number { get; set; }

    /// <summary>
    /// 科主任
    /// </summary>
    public string department_director { get; set; }

    /// <summary>
    /// 主（副主）任医师执行编号
    /// </summary>
    public string chief_physician_execution_number { get; set; }

    /// <summary>
    /// 主（副主）任医师
    /// </summary>
    public string chief_physician { get; set; }

    /// <summary>
    /// 主治医师执行编号
    /// </summary>
    public string attending_physician_execution_number { get; set; }

    /// <summary>
    /// 主治医师
    /// </summary>
    public string attending_physician { get; set; }

    /// <summary>
    /// 住院医师执行编号
    /// </summary>
    public string resident_physician_execution_number { get; set; }

    /// <summary>
    /// 住院医师
    /// </summary>
    public string resident_physician { get; set; }

    /// <summary>
    /// 责任护士执行编号
    /// </summary>
    public string responsible_nurse_execution_number { get; set; }

    /// <summary>
    /// 责任护士
    /// </summary>
    public string responsible_nurse { get; set; }

    /// <summary>
    /// 进修医师
    /// </summary>
    public string visiting_physician { get; set; }

    /// <summary>
    /// 实习医师
    /// </summary>
    public string intern_physician { get; set; }

    /// <summary>
    /// 编码员
    /// </summary>
    public string coder { get; set; }

    /// <summary>
    /// 病案质量
    /// </summary>
    public string medical_record_quality { get; set; }

    /// <summary>
    /// 质控医师
    /// </summary>
    public string quality_control_physician { get; set; }

    /// <summary>
    /// 质控护师
    /// </summary>
    public string quality_control_nurse { get; set; }

    /// <summary>
    /// 质控日期
    /// </summary>
    public string quality_control_date { get; set; }

    /// <summary>
    /// 质控护士执行代码
    /// </summary>
    public string quality_control_nurse_execution_code { get; set; }

    public virtual patient_base link { get; set; }
}