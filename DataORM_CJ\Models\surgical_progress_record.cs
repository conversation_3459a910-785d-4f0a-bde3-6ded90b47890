﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 患者手术病程记录表
/// </summary>
public partial class surgical_progress_record
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    /// <summary>
    /// 住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string patient_name { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int age { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string gender { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateOnly birth_date { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    public DateOnly admission_time { get; set; }

    /// <summary>
    /// 出院时间
    /// </summary>
    public DateOnly? discharge_time { get; set; }

    /// <summary>
    /// 手术日期
    /// </summary>
    public string surgery_date { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public string start_time { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public string end_time { get; set; }

    /// <summary>
    /// 手术时长
    /// </summary>
    public string duration { get; set; }

    /// <summary>
    /// 手术名称
    /// </summary>
    public string surgery_name { get; set; }

    /// <summary>
    /// 手术医生
    /// </summary>
    public string surgeon { get; set; }

    /// <summary>
    /// 第一助手
    /// </summary>
    public string first_assistant { get; set; }

    /// <summary>
    /// 第二助手
    /// </summary>
    public string second_assistant { get; set; }

    /// <summary>
    /// 手术记录
    /// </summary>
    public string surgery_notes { get; set; }

    public virtual patient_base link { get; set; }
}