﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 医嘱数据表
/// </summary>
public partial class medical_order
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    /// <summary>
    /// 住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string patient_name { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int age { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string gender { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateOnly birth_date { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    public DateOnly admission_time { get; set; }

    /// <summary>
    /// 出院时间
    /// </summary>
    public DateOnly? discharge_time { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime start_time { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? end_time { get; set; }

    /// <summary>
    /// 医嘱名称
    /// </summary>
    public string order_name { get; set; }

    /// <summary>
    /// 频次
    /// </summary>
    public string frequency { get; set; }

    /// <summary>
    /// 用法
    /// </summary>
    public string usage_method { get; set; }

    /// <summary>
    /// 剂量
    /// </summary>
    public double? dosage { get; set; }

    public virtual patient_base link { get; set; }
}