﻿{
   "CodeGenerationMode": 4,
   "ContextClassName": "chinaContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Models",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "DataORM_CJ",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 2,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "etl.ards_data_etl_rules",
         "ObjectType": 0
      },
      {
         "Name": "etl.patient_extractions",
         "ObjectType": 0
      },
      {
         "Name": "public.blood_transfusion",
         "ObjectType": 0
      },
      {
         "Name": "public.cost_info",
         "ObjectType": 0
      },
      {
         "Name": "public.diagnosis_info",
         "ObjectType": 0
      },
      {
         "Name": "public.electronic_medical_record",
         "ObjectType": 0
      },
      {
         "Name": "public.hospitalization_record",
         "ObjectType": 0
      },
      {
         "Name": "public.icu_record",
         "ObjectType": 0
      },
      {
         "Name": "public.imaging_examination_record",
         "ObjectType": 0
      },
      {
         "Name": "public.laboratory_test_record",
         "ObjectType": 0
      },
      {
         "Name": "public.medical_cost_detail",
         "ObjectType": 0
      },
      {
         "Name": "public.medical_order",
         "ObjectType": 0
      },
      {
         "Name": "public.medical_record",
         "ObjectType": 0
      },
      {
         "Name": "public.medical_record_front_page",
         "ObjectType": 0
      },
      {
         "Name": "public.nursing_level",
         "ObjectType": 0
      },
      {
         "Name": "public.nursing_record",
         "ObjectType": 0
      },
      {
         "Name": "public.operation_info",
         "ObjectType": 0
      },
      {
         "Name": "public.patient_base",
         "ObjectType": 0
      },
      {
         "Name": "public.patient_basic_info",
         "ObjectType": 0
      },
      {
         "Name": "public.patient_blood_gas_results",
         "ObjectType": 0
      },
      {
         "Name": "public.patient_diagnosis",
         "ObjectType": 0
      },
      {
         "Name": "public.patient_progress_record",
         "ObjectType": 0
      },
      {
         "Name": "public.respiratory_support",
         "ObjectType": 0
      },
      {
         "Name": "public.surgical_operation",
         "ObjectType": 0
      },
      {
         "Name": "public.surgical_progress_record",
         "ObjectType": 0
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": true,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": true,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": false,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}