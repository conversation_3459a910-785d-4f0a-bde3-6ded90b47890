﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 手术操作表
/// </summary>
public partial class surgical_operation
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    public DateOnly admission_time { get; set; }

    /// <summary>
    /// 手术类型
    /// </summary>
    public string operation_type { get; set; }

    /// <summary>
    /// 手术序号
    /// </summary>
    public int operation_seq { get; set; }

    /// <summary>
    /// 手术操作编码
    /// </summary>
    public string operation_code { get; set; }

    /// <summary>
    /// 手术操作名称
    /// </summary>
    public string operation_name { get; set; }

    /// <summary>
    /// 手术操作日期
    /// </summary>
    public DateOnly? operation_date { get; set; }

    /// <summary>
    /// 手术操作级别
    /// </summary>
    public string operation_level { get; set; }

    /// <summary>
    /// 手术操作持续时间
    /// </summary>
    public int? operation_duration { get; set; }

    /// <summary>
    /// 手术操作术者
    /// </summary>
    public string surgeon { get; set; }

    /// <summary>
    /// 手术操作Ⅰ助
    /// </summary>
    public string first_assistant { get; set; }

    /// <summary>
    /// 手术操作Ⅱ助
    /// </summary>
    public string second_assistant { get; set; }

    /// <summary>
    /// 手术操作切口愈合等级
    /// </summary>
    public string incision_healing_grade { get; set; }

    /// <summary>
    /// 手术操作麻醉方式
    /// </summary>
    public string anesthesia_method { get; set; }

    /// <summary>
    /// 手术操作麻醉分级
    /// </summary>
    public string anesthesia_level { get; set; }

    /// <summary>
    /// 手术操作麻醉医师
    /// </summary>
    public string anesthesiologist { get; set; }

    public virtual patient_base link { get; set; }
}