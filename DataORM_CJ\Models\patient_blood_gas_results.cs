﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

public partial class patient_blood_gas_results
{
    public string patient_serial_number { get; set; }

    public string blood_gas_id { get; set; }

    public DateTime test_time { get; set; }

    public string AaDpO2 { get; set; }

    public string AaDpO2_T { get; set; }

    public string ABE { get; set; }

    public string Anion_gap { get; set; }

    public string Blood_Type { get; set; }

    public string Ca__ { get; set; }

    public string Ca_7_4_ { get; set; }

    public string cBase_B__c { get; set; }

    public string cBase_Ecf__c { get; set; }

    public string cBase_Ecf_ox_ { get; set; }

    public string cHCO3__P_st__c { get; set; }

    public string Cl_ { get; set; }

    public string COHb { get; set; }

    public string FiO2 { get; set; }

    public string Glu { get; set; }

    public string HCO3_ { get; set; }

    public string Hct { get; set; }

    public string K_ { get; set; }

    public string Lac { get; set; }

    public string MetHb { get; set; }

    public string Na_ { get; set; }

    public string O2Hb { get; set; }

    public string p50_act_ { get; set; }

    public string pCO2 { get; set; }

    public string pCO2_T_ { get; set; }

    public string pH { get; set; }

    public string pH_T_ { get; set; }

    public string pO2 { get; set; }

    public string pO2_a__FIO2 { get; set; }

    public string pO2_T_ { get; set; }

    public string RHb { get; set; }

    public string RI { get; set; }

    public string SBC { get; set; }

    public string sO2 { get; set; }

    public string T { get; set; }

    public string tCO2_B_ { get; set; }

    public string tCO2_P_ { get; set; }

    public string tHb { get; set; }

    public string tO2 { get; set; }

    public DateTime? created_at { get; set; }
}