﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 诊断信息表
/// </summary>
public partial class diagnosis_info
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    /// <summary>
    /// 病案号/住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 入院诊断编码
    /// </summary>
    public string admission_diagnosis_code { get; set; }

    /// <summary>
    /// 入院诊断名称
    /// </summary>
    public string admission_diagnosis_name { get; set; }

    /// <summary>
    /// 入院后确诊日期
    /// </summary>
    public string confirmed_diagnosis_date { get; set; }

    /// <summary>
    /// 出院主要诊断编码
    /// </summary>
    public string main_discharge_diagnosis_code { get; set; }

    /// <summary>
    /// 出院主要诊断名称
    /// </summary>
    public string main_discharge_diagnosis_name { get; set; }

    /// <summary>
    /// 出院主要诊断入院病情
    /// </summary>
    public string main_discharge_diagnosis_admission_condition { get; set; }

    /// <summary>
    /// 主要诊断出院情况
    /// </summary>
    public string main_discharge_diagnosis_discharge_condition { get; set; }

    /// <summary>
    /// 出院其他诊断编码数组
    /// </summary>
    public List<string> other_diagnosis_codes { get; set; }

    /// <summary>
    /// 出院其他诊断名称数组
    /// </summary>
    public List<string> other_diagnosis_names { get; set; }

    /// <summary>
    /// 出院其他诊断入院病情数组
    /// </summary>
    public List<string> other_diagnosis_admission_conditions { get; set; }

    /// <summary>
    /// 出院其他诊断出院情况数组
    /// </summary>
    public List<string> other_diagnosis_discharge_conditions { get; set; }

    /// <summary>
    /// 病理诊断编码数组
    /// </summary>
    public List<string> pathological_diagnosis_codes { get; set; }

    /// <summary>
    /// 病理诊断名称数组
    /// </summary>
    public List<string> pathological_diagnosis_names { get; set; }

    /// <summary>
    /// 病理号数组
    /// </summary>
    public List<string> pathological_numbers { get; set; }

    /// <summary>
    /// 损伤、中毒外部原因编码
    /// </summary>
    public string injury_poisoning_external_cause_code { get; set; }

    /// <summary>
    /// 损伤、中毒外部原因名称
    /// </summary>
    public string injury_poisoning_external_cause_name { get; set; }

    public virtual patient_base link { get; set; }
}