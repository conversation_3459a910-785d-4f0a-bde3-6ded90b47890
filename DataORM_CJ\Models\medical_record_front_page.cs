﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 患者病案首页表
/// </summary>
public partial class medical_record_front_page
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    /// <summary>
    /// 住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string patient_name { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int age { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string gender { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateOnly birth_date { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    public DateOnly admission_time { get; set; }

    /// <summary>
    /// 出院时间
    /// </summary>
    public DateOnly discharge_time { get; set; }

    /// <summary>
    /// 组织机构代码
    /// </summary>
    public string organization_code { get; set; }

    /// <summary>
    /// 医疗机构名称
    /// </summary>
    public string hospital_name { get; set; }

    /// <summary>
    /// 病案号
    /// </summary>
    public string medical_record_number { get; set; }

    /// <summary>
    /// 住院次数
    /// </summary>
    public int? hospitalization_number { get; set; }

    /// <summary>
    /// 医疗付费方式
    /// </summary>
    public string payment_method { get; set; }

    /// <summary>
    /// 国籍
    /// </summary>
    public string nationality { get; set; }

    /// <summary>
    /// 婚姻
    /// </summary>
    public string marital_status { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public string occupation { get; set; }

    /// <summary>
    /// 民族
    /// </summary>
    public string ethnicity { get; set; }

    /// <summary>
    /// 证件类别
    /// </summary>
    public string id_type { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string id_number { get; set; }

    /// <summary>
    /// 出生地址
    /// </summary>
    public string birth_address { get; set; }

    /// <summary>
    /// 户口地址
    /// </summary>
    public string registered_residence { get; set; }

    /// <summary>
    /// 户口地址邮政编码
    /// </summary>
    public string registered_residence_postcode { get; set; }

    /// <summary>
    /// 现住址
    /// </summary>
    public string current_address { get; set; }

    /// <summary>
    /// 现住址电话
    /// </summary>
    public string current_address_phone { get; set; }

    /// <summary>
    /// 现住址邮政编码
    /// </summary>
    public string current_address_postcode { get; set; }

    /// <summary>
    /// 工作单位及地址
    /// </summary>
    public string work_unit_address { get; set; }

    /// <summary>
    /// 工作单位电话
    /// </summary>
    public string work_unit_phone { get; set; }

    /// <summary>
    /// 工作单位邮政编码
    /// </summary>
    public string work_unit_postcode { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    public string contact_name { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>
    public string contact_relationship { get; set; }

    /// <summary>
    /// 联系人地址
    /// </summary>
    public string contact_address { get; set; }

    /// <summary>
    /// 联系人电话
    /// </summary>
    public string contact_phone { get; set; }

    /// <summary>
    /// 是否为日间手术
    /// </summary>
    public bool? is_day_surgery { get; set; }

    /// <summary>
    /// 入院途径
    /// </summary>
    public string admission_route { get; set; }

    /// <summary>
    /// 入院科别
    /// </summary>
    public string admission_department { get; set; }

    /// <summary>
    /// 入院病房
    /// </summary>
    public string admission_ward { get; set; }

    /// <summary>
    /// 转科科别
    /// </summary>
    public string transfer_department { get; set; }

    /// <summary>
    /// 出院科别
    /// </summary>
    public string discharge_department { get; set; }

    /// <summary>
    /// 出院病房
    /// </summary>
    public string discharge_ward { get; set; }

    /// <summary>
    /// 实际住院（天）
    /// </summary>
    public int? actual_days_in_hospital { get; set; }

    /// <summary>
    /// 门（急）诊诊断编码
    /// </summary>
    public string emergency_diagnosis_code { get; set; }

    /// <summary>
    /// 门（急）诊诊断名称
    /// </summary>
    public string emergency_diagnosis_name { get; set; }

    /// <summary>
    /// 年龄不足1周岁的年龄（天）
    /// </summary>
    public int? days_for_under_one_year { get; set; }

    /// <summary>
    /// 是否有出院31日内再住院计划
    /// </summary>
    public bool? readmission_plan_in_31_days { get; set; }

    /// <summary>
    /// 出院31天再住院计划目的
    /// </summary>
    public string readmission_plan_purpose { get; set; }

    /// <summary>
    /// 离院方式
    /// </summary>
    public string leave_type { get; set; }

    /// <summary>
    /// 医嘱转院、转社区卫生服务机构/乡镇卫生院名称
    /// </summary>
    public string transfer_hospital_name { get; set; }

    /// <summary>
    /// 入院时情况
    /// </summary>
    public string condition_on_admission { get; set; }

    /// <summary>
    /// 入院诊断编码
    /// </summary>
    public string admission_diagnosis_code { get; set; }

    /// <summary>
    /// 入院诊断名称
    /// </summary>
    public string admission_diagnosis_name { get; set; }

    /// <summary>
    /// 入院后确诊日期
    /// </summary>
    public DateOnly? confirmed_diagnosis_date { get; set; }

    /// <summary>
    /// 出院主要诊断编码
    /// </summary>
    public string discharge_main_diagnosis_code { get; set; }

    /// <summary>
    /// 出院主要诊断名称
    /// </summary>
    public string discharge_main_diagnosis_name { get; set; }

    /// <summary>
    /// 主要诊断入院病情
    /// </summary>
    public string main_diagnosis_condition_on_admission { get; set; }

    /// <summary>
    /// 主要诊断出院情况
    /// </summary>
    public string main_diagnosis_condition_on_discharge { get; set; }

    /// <summary>
    /// 有无药物过敏
    /// </summary>
    public bool? allergic_drug_flag { get; set; }

    /// <summary>
    /// 过敏药物名称
    /// </summary>
    public string allergic_drug_name { get; set; }

    /// <summary>
    /// ABO血型
    /// </summary>
    public string blood_type_abo { get; set; }

    /// <summary>
    /// Rh血型
    /// </summary>
    public string blood_type_rh { get; set; }

    /// <summary>
    /// 住院总费用
    /// </summary>
    public decimal? total_cost { get; set; }

    /// <summary>
    /// 住院总费用其中自付金额
    /// </summary>
    public decimal? self_payment { get; set; }

    /// <summary>
    /// 科主任执行编号
    /// </summary>
    public string director_code { get; set; }

    /// <summary>
    /// 科主任
    /// </summary>
    public string director_name { get; set; }

    /// <summary>
    /// 主（副主）任医师执行编号
    /// </summary>
    public string attending_doctor_code { get; set; }

    /// <summary>
    /// 主（副主）任医师
    /// </summary>
    public string attending_doctor_name { get; set; }

    /// <summary>
    /// 住院医师执行编号
    /// </summary>
    public string resident_doctor_code { get; set; }

    /// <summary>
    /// 住院医师
    /// </summary>
    public string resident_doctor_name { get; set; }

    /// <summary>
    /// 责任护士执行编号
    /// </summary>
    public string responsible_nurse_code { get; set; }

    /// <summary>
    /// 责任护士
    /// </summary>
    public string responsible_nurse_name { get; set; }

    /// <summary>
    /// 质控医师
    /// </summary>
    public string quality_control_doctor { get; set; }

    /// <summary>
    /// 质控护师
    /// </summary>
    public string quality_control_nurse { get; set; }

    /// <summary>
    /// 质控日期
    /// </summary>
    public DateOnly? quality_control_date { get; set; }

    public virtual patient_base link { get; set; }
}