using DataORM_CJ.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.ComponentModel;

namespace ControlCenter
{
    public partial class EtlRulesControl : UserControl
    {
        private chinaContext? _context;
        private BindingList<ards_data_etl_rules>? _etlRules;

        /// <summary>
        /// 状态变化事件
        /// </summary>
        public event EventHandler<string>? StatusChanged;

        /// <summary>
        /// ETL规则选择事件
        /// </summary>
        public event EventHandler<ards_data_etl_rules>? EtlRuleSelected;

        public EtlRulesControl()
        {
            InitializeComponent();
            if (!DesignMode)
            {
                InitializeDataGridView();
            }
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        public async Task InitializeAsync(string? connectionString = null)
        {
            try
            {
                OnStatusChanged("正在初始化ETL规则控件...");

                // 初始化数据库连接
                InitializeDatabase(connectionString);

                // 加载ETL规则数据
                await LoadEtlRulesDataAsync();

                OnStatusChanged("ETL规则控件初始化完成");
            }
            catch (Exception ex)
            {
                OnStatusChanged("ETL规则控件初始化失败");
                MessageBox.Show($"ETL规则控件初始化失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        public async Task RefreshDataAsync()
        {
            await LoadEtlRulesDataAsync();
        }

        /// <summary>
        /// 获取当前选中的ETL规则
        /// </summary>
        public ards_data_etl_rules? GetSelectedEtlRule()
        {
            if (dataGridViewEtlRules.CurrentRow?.DataBoundItem is ards_data_etl_rules rule)
            {
                return rule;
            }
            return null;
        }

        /// <summary>
        /// 获取DataGridView控件（用于外部访问）
        /// </summary>
        public DataGridView GetDataGridView()
        {
            return dataGridViewEtlRules;
        }

        private void InitializeDatabase(string? connectionString = null)
        {
            if (string.IsNullOrEmpty(connectionString))
            {
                // 读取配置文件
                var configuration = new ConfigurationBuilder()
                    .AddJsonFile("appsettings.json", optional: true)
                    .Build();

                connectionString = configuration.GetConnectionString("DefaultConnection");
            }

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("未找到数据库连接字符串");
            }

            var optionsBuilder = new DbContextOptionsBuilder<chinaContext>();
            optionsBuilder.UseNpgsql(connectionString);
            _context = new chinaContext(optionsBuilder.Options);
        }

        private async Task LoadEtlRulesDataAsync()
        {
            if (_context == null) return;

            try
            {
                // 显示加载提示
                dataGridViewEtlRules.DataSource = null;
                OnStatusChanged("正在加载ETL规则数据...");

                // 异步加载数据
                var etlRulesData = await _context.ards_data_etl_rules
                    .OrderBy(r => r.task_code)
                    .ToListAsync();

                _etlRules = new BindingList<ards_data_etl_rules>(etlRulesData);

                // 绑定数据
                dataGridViewEtlRules.DataSource = _etlRules;

                // 应用列显示设置
                ApplyColumnSettings();

                OnStatusChanged($"共加载 {_etlRules.Count} 条ETL规则记录");
            }
            catch (Exception ex)
            {
                OnStatusChanged("ETL规则数据加载失败");
                MessageBox.Show($"加载ETL规则数据失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataGridView()
        {
            // 设置DataGridView的基本属性
            dataGridViewEtlRules.SelectionChanged += DataGridViewEtlRules_SelectionChanged;
            dataGridViewEtlRules.AutoGenerateColumns = true;
            dataGridViewEtlRules.AllowUserToResizeColumns = true;
            dataGridViewEtlRules.AllowUserToOrderColumns = true;
        }

        private void ApplyColumnSettings()
        {
            if (dataGridViewEtlRules.Columns.Count == 0) return;

            // 设置中文列标题
            var columnMappings = new Dictionary<string, string>
            {
                { "task_code", "任务编号" },
                { "is_extract", "是否提取" },
                { "dict_name", "字典名称" },
                { "extract_location", "提取位置" },
                { "remark", "备注说明" },
                { "prompt", "LLM提示词" },
                { "sql_script", "SQL脚本" }
            };

            foreach (DataGridViewColumn column in dataGridViewEtlRules.Columns)
            {
                if (columnMappings.ContainsKey(column.Name))
                {
                    column.HeaderText = columnMappings[column.Name];
                }

                // 设置列宽
                switch (column.Name)
                {
                    case "task_code":
                        column.Width = 120;
                        column.FillWeight = 15;
                        break;
                    case "is_extract":
                        column.Width = 80;
                        column.FillWeight = 10;
                        break;
                    case "dict_name":
                        column.Width = 150;
                        column.FillWeight = 20;
                        break;
                    case "extract_location":
                        column.Width = 100;
                        column.FillWeight = 15;
                        break;
                    case "remark":
                        column.Width = 200;
                        column.FillWeight = 25;
                        break;
                    case "prompt":
                        column.Width = 150;
                        column.FillWeight = 20;
                        break;
                    case "sql_script":
                        column.Width = 150;
                        column.FillWeight = 20;
                        break;
                }

                // 设置文本换行
                column.DefaultCellStyle.WrapMode = DataGridViewTriState.True;
            }

            // 设置行高自动调整
            dataGridViewEtlRules.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
        }

        private void DataGridViewEtlRules_SelectionChanged(object? sender, EventArgs e)
        {
            var selectedRule = GetSelectedEtlRule();
            if (selectedRule != null)
            {
                EtlRuleSelected?.Invoke(this, selectedRule);
            }
        }

        private void OnStatusChanged(string status)
        {
            toolStripStatusLabel.Text = status;
            StatusChanged?.Invoke(this, status);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
                components?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
