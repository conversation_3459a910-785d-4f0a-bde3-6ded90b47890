﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 患者基本信息表
/// </summary>
public partial class patient_basic_info
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    /// <summary>
    /// 病案号/住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 健康卡号
    /// </summary>
    public string health_card_number { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string gender { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public string age { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public string birth_date { get; set; }

    /// <summary>
    /// 国籍
    /// </summary>
    public string nationality { get; set; }

    /// <summary>
    /// 婚姻
    /// </summary>
    public string marital_status { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public string occupation { get; set; }

    /// <summary>
    /// 民族
    /// </summary>
    public string ethnicity { get; set; }

    /// <summary>
    /// 证件类别
    /// </summary>
    public string id_type { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string id_number { get; set; }

    /// <summary>
    /// 出生地址
    /// </summary>
    public string birth_address { get; set; }

    /// <summary>
    /// 籍贯省（自治区、直辖市）
    /// </summary>
    public string native_province { get; set; }

    /// <summary>
    /// 户口地址
    /// </summary>
    public string household_address { get; set; }

    /// <summary>
    /// 户口地址邮政编码
    /// </summary>
    public string household_postal_code { get; set; }

    /// <summary>
    /// 现住址
    /// </summary>
    public string current_address { get; set; }

    /// <summary>
    /// 现住址电话
    /// </summary>
    public string current_address_phone { get; set; }

    /// <summary>
    /// 现住址邮政编码
    /// </summary>
    public string current_address_postal_code { get; set; }

    /// <summary>
    /// 工作单位及地址
    /// </summary>
    public string work_unit_address { get; set; }

    /// <summary>
    /// 工作单位电话
    /// </summary>
    public string work_unit_phone { get; set; }

    /// <summary>
    /// 工作单位邮政编码
    /// </summary>
    public string work_unit_postal_code { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    public string contact_name { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>
    public string contact_relationship { get; set; }

    /// <summary>
    /// 联系人地址
    /// </summary>
    public string contact_address { get; set; }

    /// <summary>
    /// 联系人电话
    /// </summary>
    public string contact_phone { get; set; }

    /// <summary>
    /// 有无药物过敏
    /// </summary>
    public string has_drug_allergy { get; set; }

    /// <summary>
    /// 过敏药物名称
    /// </summary>
    public string allergic_drug_name { get; set; }

    /// <summary>
    /// HBsAg
    /// </summary>
    public string hbsag { get; set; }

    /// <summary>
    /// HCV-Ab
    /// </summary>
    public string hcv_ab { get; set; }

    /// <summary>
    /// HIV-Ab
    /// </summary>
    public string hiv_ab { get; set; }

    /// <summary>
    /// ABO血型
    /// </summary>
    public string abo_blood_type { get; set; }

    /// <summary>
    /// Rh血型
    /// </summary>
    public string rh_blood_type { get; set; }

    public virtual patient_base link { get; set; }

    public virtual ICollection<patient_extractions> patient_extractions { get; set; } = new List<patient_extractions>();
}