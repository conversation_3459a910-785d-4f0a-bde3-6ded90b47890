﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 患者诊断表
/// </summary>
public partial class patient_diagnosis
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    public DateOnly admission_time { get; set; }

    /// <summary>
    /// 诊断类型
    /// </summary>
    public string diagnosis_type { get; set; }

    /// <summary>
    /// 诊断序号
    /// </summary>
    public int diagnosis_seq { get; set; }

    /// <summary>
    /// 诊断编码
    /// </summary>
    public string diagnosis_code { get; set; }

    /// <summary>
    /// 诊断名称
    /// </summary>
    public string diagnosis_name { get; set; }

    /// <summary>
    /// 入院病情
    /// </summary>
    public string condition_on_admission { get; set; }

    /// <summary>
    /// 出院情况
    /// </summary>
    public string condition_on_discharge { get; set; }

    /// <summary>
    /// 病理号
    /// </summary>
    public string pathology_number { get; set; }

    public virtual patient_base link { get; set; }
}