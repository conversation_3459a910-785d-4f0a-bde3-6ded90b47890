using DataORM_CJ.Models;
using System.Reflection;

namespace ControlCenter
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private async void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                // 绑定患者列表控件事件
                patientListControl1.StatusChanged += PatientListControl1_StatusChanged;
                patientListControl1.PatientSelected += PatientListControl1_PatientSelected;
                patientListControl1.PatientsSelected += PatientListControl1_PatientsSelected;

                // 初始化患者列表
                await patientListControl1.InitializeAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用初始化失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PatientListControl1_StatusChanged(object? sender, string status)
        {
            // 更新主窗体状态栏
            toolStripStatusLabel1.Text = status;
        }

        private void PatientListControl1_PatientSelected(object? sender, patient_basic_info patient)
        {
            // 患者选择事件处理 - 可以在右区域显示患者详细信息
            labelRight.Text = $"选中患者：{patient.name}\n性别：{patient.gender}\n年龄：{patient.age}";
        }

        private void PatientListControl1_PatientsSelected(object? sender, List<patient_basic_info> patients)
        {
            // 多选患者事件处理 - 在下方区域显示所有选中患者的详细信息
            if (patients == null || patients.Count == 0)
            {
                txtSelectedPatientsDetails.Text = "";
                return;
            }

            var result = new System.Text.StringBuilder();
            
            foreach (var patient in patients)
            {
                // 获取所有可见列的信息
                var values = new List<string>();
                
                // 遍历DataGridView的可见列，按显示顺序获取数据
                foreach (DataGridViewColumn column in patientListControl1.GetDataGridView().Columns)
                {
                    if (column.Visible)
                    {
                        var property = typeof(patient_basic_info).GetProperty(column.DataPropertyName);
                        if (property != null)
                        {
                            var value = property.GetValue(patient);
                            values.Add(value?.ToString() ?? "");
                        }
                    }
                }
                
                // 用逗号连接所有值
                result.AppendLine(string.Join(",", values));
            }
            
            txtSelectedPatientsDetails.Text = result.ToString();
        }
    }
}
