/*
 Navicat Premium Dump SQL

 Source Server         : localhost-PG
 Source Server Type    : PostgreSQL
 Source Server Version : 170004 (170004)
 Source Host           : localhost:5432
 Source Catalog        : china
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170004 (170004)
 File Encoding         : 65001

 Date: 26/05/2025 15:00:08
*/


-- ----------------------------
-- Table structure for blood_transfusion
-- ----------------------------
DROP TABLE IF EXISTS "public"."blood_transfusion";
CREATE TABLE "public"."blood_transfusion" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "admission_time" date NOT NULL,
  "blood_type" text COLLATE "pg_catalog"."default" NOT NULL,
  "amount" float8,
  "reaction" bool
)
;
COMMENT ON COLUMN "public"."blood_transfusion"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."blood_transfusion"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."blood_transfusion"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."blood_transfusion"."blood_type" IS '血液类型';
COMMENT ON COLUMN "public"."blood_transfusion"."amount" IS '数量';
COMMENT ON COLUMN "public"."blood_transfusion"."reaction" IS '输血反应';
COMMENT ON TABLE "public"."blood_transfusion" IS '输血记录表';

-- ----------------------------
-- Table structure for cost_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_info";
CREATE TABLE "public"."cost_info" (
  "link_id" text COLLATE "pg_catalog"."default",
  "unique_id" text COLLATE "pg_catalog"."default",
  "record_id" text COLLATE "pg_catalog"."default",
  "total_cost" text COLLATE "pg_catalog"."default",
  "self_payment_amount" text COLLATE "pg_catalog"."default",
  "general_medical_service_fee" text COLLATE "pg_catalog"."default",
  "general_treatment_operation_fee" text COLLATE "pg_catalog"."default",
  "nursing_fee" text COLLATE "pg_catalog"."default",
  "other_comprehensive_medical_service_fee" text COLLATE "pg_catalog"."default",
  "pathological_diagnosis_fee" text COLLATE "pg_catalog"."default",
  "laboratory_diagnosis_fee" text COLLATE "pg_catalog"."default",
  "imaging_diagnosis_fee" text COLLATE "pg_catalog"."default",
  "clinical_diagnosis_project_fee" text COLLATE "pg_catalog"."default",
  "non_surgical_treatment_project_fee" text COLLATE "pg_catalog"."default",
  "clinical_physical_therapy_fee" text COLLATE "pg_catalog"."default",
  "surgical_treatment_fee" text COLLATE "pg_catalog"."default",
  "anesthesia_fee" text COLLATE "pg_catalog"."default",
  "surgery_fee" text COLLATE "pg_catalog"."default",
  "rehabilitation_fee" text COLLATE "pg_catalog"."default",
  "traditional_chinese_medicine_treatment_fee" text COLLATE "pg_catalog"."default",
  "western_medicine_fee" text COLLATE "pg_catalog"."default",
  "antibacterial_drug_fee" text COLLATE "pg_catalog"."default",
  "chinese_patent_medicine_fee" text COLLATE "pg_catalog"."default",
  "chinese_herbal_medicine_fee" text COLLATE "pg_catalog"."default",
  "blood_fee" text COLLATE "pg_catalog"."default",
  "albumin_product_fee" text COLLATE "pg_catalog"."default",
  "globulin_product_fee" text COLLATE "pg_catalog"."default",
  "coagulation_factor_product_fee" text COLLATE "pg_catalog"."default",
  "cytokine_product_fee" text COLLATE "pg_catalog"."default",
  "examination_disposable_medical_material_fee" text COLLATE "pg_catalog"."default",
  "treatment_disposable_medical_material_fee" text COLLATE "pg_catalog"."default",
  "surgical_disposable_medical_material_fee" text COLLATE "pg_catalog"."default",
  "other_fee" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."cost_info"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."cost_info"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."cost_info"."record_id" IS '病案号/住院号/门诊号';
COMMENT ON COLUMN "public"."cost_info"."total_cost" IS '住院总费用';
COMMENT ON COLUMN "public"."cost_info"."self_payment_amount" IS '住院总费用其中自付金额';
COMMENT ON COLUMN "public"."cost_info"."general_medical_service_fee" IS '1.一般医疗服务费';
COMMENT ON COLUMN "public"."cost_info"."general_treatment_operation_fee" IS '2.一般治疗操作费';
COMMENT ON COLUMN "public"."cost_info"."nursing_fee" IS '3.护理费';
COMMENT ON COLUMN "public"."cost_info"."other_comprehensive_medical_service_fee" IS '4.综合医疗服务类其他费用';
COMMENT ON COLUMN "public"."cost_info"."pathological_diagnosis_fee" IS '5.病理诊断费';
COMMENT ON COLUMN "public"."cost_info"."laboratory_diagnosis_fee" IS '6.实验室诊断费';
COMMENT ON COLUMN "public"."cost_info"."imaging_diagnosis_fee" IS '7.影像学诊断费';
COMMENT ON COLUMN "public"."cost_info"."clinical_diagnosis_project_fee" IS '8.临床诊断项目费';
COMMENT ON COLUMN "public"."cost_info"."non_surgical_treatment_project_fee" IS '9.非手术治疗项目费';
COMMENT ON COLUMN "public"."cost_info"."clinical_physical_therapy_fee" IS '其中：临床物理治疗费';
COMMENT ON COLUMN "public"."cost_info"."surgical_treatment_fee" IS '10.手术治疗费';
COMMENT ON COLUMN "public"."cost_info"."anesthesia_fee" IS '其中：麻醉费';
COMMENT ON COLUMN "public"."cost_info"."surgery_fee" IS '其中：手术费';
COMMENT ON COLUMN "public"."cost_info"."rehabilitation_fee" IS '11.康复费';
COMMENT ON COLUMN "public"."cost_info"."traditional_chinese_medicine_treatment_fee" IS '12.中医治疗费';
COMMENT ON COLUMN "public"."cost_info"."western_medicine_fee" IS '13.西药费';
COMMENT ON COLUMN "public"."cost_info"."antibacterial_drug_fee" IS '其中：抗菌药物费';
COMMENT ON COLUMN "public"."cost_info"."chinese_patent_medicine_fee" IS '14.中成药费';
COMMENT ON COLUMN "public"."cost_info"."chinese_herbal_medicine_fee" IS '15.中草药费';
COMMENT ON COLUMN "public"."cost_info"."blood_fee" IS '16.血费';
COMMENT ON COLUMN "public"."cost_info"."albumin_product_fee" IS '17.白蛋白类制品费';
COMMENT ON COLUMN "public"."cost_info"."globulin_product_fee" IS '18.球蛋白类制品费';
COMMENT ON COLUMN "public"."cost_info"."coagulation_factor_product_fee" IS '19.凝血因子类制品费';
COMMENT ON COLUMN "public"."cost_info"."cytokine_product_fee" IS '20.细胞因子类制品费';
COMMENT ON COLUMN "public"."cost_info"."examination_disposable_medical_material_fee" IS '21.检查用一次性医用材料费';
COMMENT ON COLUMN "public"."cost_info"."treatment_disposable_medical_material_fee" IS '22.治疗用一次性医用材料费';
COMMENT ON COLUMN "public"."cost_info"."surgical_disposable_medical_material_fee" IS '23.手术用一次性医用材料费';
COMMENT ON COLUMN "public"."cost_info"."other_fee" IS '24.其他费';
COMMENT ON TABLE "public"."cost_info" IS '费用信息表';

-- ----------------------------
-- Table structure for diagnosis_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."diagnosis_info";
CREATE TABLE "public"."diagnosis_info" (
  "link_id" text COLLATE "pg_catalog"."default",
  "unique_id" text COLLATE "pg_catalog"."default",
  "record_id" text COLLATE "pg_catalog"."default",
  "admission_diagnosis_code" text COLLATE "pg_catalog"."default",
  "admission_diagnosis_name" text COLLATE "pg_catalog"."default",
  "confirmed_diagnosis_date" text COLLATE "pg_catalog"."default",
  "main_discharge_diagnosis_code" text COLLATE "pg_catalog"."default",
  "main_discharge_diagnosis_name" text COLLATE "pg_catalog"."default",
  "main_discharge_diagnosis_admission_condition" text COLLATE "pg_catalog"."default",
  "main_discharge_diagnosis_discharge_condition" text COLLATE "pg_catalog"."default",
  "other_diagnosis_codes" text[] COLLATE "pg_catalog"."default",
  "other_diagnosis_names" text[] COLLATE "pg_catalog"."default",
  "other_diagnosis_admission_conditions" text[] COLLATE "pg_catalog"."default",
  "other_diagnosis_discharge_conditions" text[] COLLATE "pg_catalog"."default",
  "pathological_diagnosis_codes" text[] COLLATE "pg_catalog"."default",
  "pathological_diagnosis_names" text[] COLLATE "pg_catalog"."default",
  "pathological_numbers" text[] COLLATE "pg_catalog"."default",
  "injury_poisoning_external_cause_code" text COLLATE "pg_catalog"."default",
  "injury_poisoning_external_cause_name" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."diagnosis_info"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."diagnosis_info"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."diagnosis_info"."record_id" IS '病案号/住院号/门诊号';
COMMENT ON COLUMN "public"."diagnosis_info"."admission_diagnosis_code" IS '入院诊断编码';
COMMENT ON COLUMN "public"."diagnosis_info"."admission_diagnosis_name" IS '入院诊断名称';
COMMENT ON COLUMN "public"."diagnosis_info"."confirmed_diagnosis_date" IS '入院后确诊日期';
COMMENT ON COLUMN "public"."diagnosis_info"."main_discharge_diagnosis_code" IS '出院主要诊断编码';
COMMENT ON COLUMN "public"."diagnosis_info"."main_discharge_diagnosis_name" IS '出院主要诊断名称';
COMMENT ON COLUMN "public"."diagnosis_info"."main_discharge_diagnosis_admission_condition" IS '出院主要诊断入院病情';
COMMENT ON COLUMN "public"."diagnosis_info"."main_discharge_diagnosis_discharge_condition" IS '主要诊断出院情况';
COMMENT ON COLUMN "public"."diagnosis_info"."other_diagnosis_codes" IS '出院其他诊断编码数组';
COMMENT ON COLUMN "public"."diagnosis_info"."other_diagnosis_names" IS '出院其他诊断名称数组';
COMMENT ON COLUMN "public"."diagnosis_info"."other_diagnosis_admission_conditions" IS '出院其他诊断入院病情数组';
COMMENT ON COLUMN "public"."diagnosis_info"."other_diagnosis_discharge_conditions" IS '出院其他诊断出院情况数组';
COMMENT ON COLUMN "public"."diagnosis_info"."pathological_diagnosis_codes" IS '病理诊断编码数组';
COMMENT ON COLUMN "public"."diagnosis_info"."pathological_diagnosis_names" IS '病理诊断名称数组';
COMMENT ON COLUMN "public"."diagnosis_info"."pathological_numbers" IS '病理号数组';
COMMENT ON COLUMN "public"."diagnosis_info"."injury_poisoning_external_cause_code" IS '损伤、中毒外部原因编码';
COMMENT ON COLUMN "public"."diagnosis_info"."injury_poisoning_external_cause_name" IS '损伤、中毒外部原因名称';
COMMENT ON TABLE "public"."diagnosis_info" IS '诊断信息表';

-- ----------------------------
-- Table structure for electronic_medical_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."electronic_medical_record";
CREATE TABLE "public"."electronic_medical_record" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "unique_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "patient_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "age" int4 NOT NULL,
  "gender" text COLLATE "pg_catalog"."default" NOT NULL,
  "birth_date" date NOT NULL,
  "admission_time" date NOT NULL,
  "discharge_time" date NOT NULL,
  "admission_diagnosis" text COLLATE "pg_catalog"."default",
  "present_illness" text COLLATE "pg_catalog"."default",
  "past_history" text COLLATE "pg_catalog"."default",
  "personal_history" text COLLATE "pg_catalog"."default",
  "allergy_history" text COLLATE "pg_catalog"."default",
  "family_history" text COLLATE "pg_catalog"."default",
  "menstrual_history" text COLLATE "pg_catalog"."default",
  "marriage_history" text COLLATE "pg_catalog"."default",
  "chief_complaint" text COLLATE "pg_catalog"."default",
  "physical_examination" text COLLATE "pg_catalog"."default",
  "discharge_diagnosis" text COLLATE "pg_catalog"."default",
  "discharge_record" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."electronic_medical_record"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."electronic_medical_record"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."electronic_medical_record"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."electronic_medical_record"."patient_name" IS '姓名';
COMMENT ON COLUMN "public"."electronic_medical_record"."age" IS '年龄';
COMMENT ON COLUMN "public"."electronic_medical_record"."gender" IS '性别';
COMMENT ON COLUMN "public"."electronic_medical_record"."birth_date" IS '出生日期';
COMMENT ON COLUMN "public"."electronic_medical_record"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."electronic_medical_record"."discharge_time" IS '出院时间';
COMMENT ON COLUMN "public"."electronic_medical_record"."admission_diagnosis" IS '入院诊断';
COMMENT ON COLUMN "public"."electronic_medical_record"."present_illness" IS '现病史';
COMMENT ON COLUMN "public"."electronic_medical_record"."past_history" IS '既往史';
COMMENT ON COLUMN "public"."electronic_medical_record"."personal_history" IS '个人史';
COMMENT ON COLUMN "public"."electronic_medical_record"."allergy_history" IS '过敏史';
COMMENT ON COLUMN "public"."electronic_medical_record"."family_history" IS '家族史';
COMMENT ON COLUMN "public"."electronic_medical_record"."menstrual_history" IS '月经史';
COMMENT ON COLUMN "public"."electronic_medical_record"."marriage_history" IS '婚育史';
COMMENT ON COLUMN "public"."electronic_medical_record"."chief_complaint" IS '主诉';
COMMENT ON COLUMN "public"."electronic_medical_record"."physical_examination" IS '体格检查';
COMMENT ON COLUMN "public"."electronic_medical_record"."discharge_diagnosis" IS '出院诊断';
COMMENT ON COLUMN "public"."electronic_medical_record"."discharge_record" IS '出院记录';
COMMENT ON TABLE "public"."electronic_medical_record" IS '患者电子病历表';

-- ----------------------------
-- Table structure for hospitalization_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."hospitalization_record";
CREATE TABLE "public"."hospitalization_record" (
  "link_id" text COLLATE "pg_catalog"."default",
  "unique_id" text COLLATE "pg_catalog"."default",
  "record_id" text COLLATE "pg_catalog"."default",
  "organization_code" text COLLATE "pg_catalog"."default",
  "medical_institution_name" text COLLATE "pg_catalog"."default",
  "hospitalization_count" text COLLATE "pg_catalog"."default",
  "admission_time" text COLLATE "pg_catalog"."default",
  "discharge_time" text COLLATE "pg_catalog"."default",
  "payment_method" text COLLATE "pg_catalog"."default",
  "is_day_surgery" text COLLATE "pg_catalog"."default",
  "admission_path" text COLLATE "pg_catalog"."default",
  "admission_department" text COLLATE "pg_catalog"."default",
  "admission_ward" text COLLATE "pg_catalog"."default",
  "transfer_department" text COLLATE "pg_catalog"."default",
  "discharge_department" text COLLATE "pg_catalog"."default",
  "discharge_ward" text COLLATE "pg_catalog"."default",
  "actual_hospitalization_days" text COLLATE "pg_catalog"."default",
  "has_rehospitalization_plan" text COLLATE "pg_catalog"."default",
  "rehospitalization_plan_purpose" text COLLATE "pg_catalog"."default",
  "discharge_method" text COLLATE "pg_catalog"."default",
  "transfer_institution_name" text COLLATE "pg_catalog"."default",
  "admission_condition" text COLLATE "pg_catalog"."default",
  "emergency_diagnosis_code" text COLLATE "pg_catalog"."default",
  "emergency_diagnosis_name" text COLLATE "pg_catalog"."default",
  "death_patient_autopsy" text COLLATE "pg_catalog"."default",
  "age_under_one_week_days" text COLLATE "pg_catalog"."default",
  "newborn_birth_weights" text[] COLLATE "pg_catalog"."default",
  "newborn_admission_weight" text COLLATE "pg_catalog"."default",
  "craniocerebral_injury_before_coma_days" text COLLATE "pg_catalog"."default",
  "craniocerebral_injury_before_coma_hours" text COLLATE "pg_catalog"."default",
  "craniocerebral_injury_before_coma_minutes" text COLLATE "pg_catalog"."default",
  "craniocerebral_injury_after_coma_days" text COLLATE "pg_catalog"."default",
  "craniocerebral_injury_after_coma_hours" text COLLATE "pg_catalog"."default",
  "craniocerebral_injury_after_coma_minutes" text COLLATE "pg_catalog"."default",
  "special_care_days" text COLLATE "pg_catalog"."default",
  "first_level_care_days" text COLLATE "pg_catalog"."default",
  "second_level_care_days" text COLLATE "pg_catalog"."default",
  "third_level_care_days" text COLLATE "pg_catalog"."default",
  "blood_transfusion_reaction" text COLLATE "pg_catalog"."default",
  "red_blood_cells" text COLLATE "pg_catalog"."default",
  "platelets" text COLLATE "pg_catalog"."default",
  "plasma" text COLLATE "pg_catalog"."default",
  "whole_blood" text COLLATE "pg_catalog"."default",
  "autologous_blood_transfusion" text COLLATE "pg_catalog"."default",
  "invasive_ventilator_time" text COLLATE "pg_catalog"."default",
  "icu_names" text[] COLLATE "pg_catalog"."default",
  "icu_entry_times" text[] COLLATE "pg_catalog"."default",
  "icu_exit_times" text[] COLLATE "pg_catalog"."default",
  "department_director_execution_number" text COLLATE "pg_catalog"."default",
  "department_director" text COLLATE "pg_catalog"."default",
  "chief_physician_execution_number" text COLLATE "pg_catalog"."default",
  "chief_physician" text COLLATE "pg_catalog"."default",
  "attending_physician_execution_number" text COLLATE "pg_catalog"."default",
  "attending_physician" text COLLATE "pg_catalog"."default",
  "resident_physician_execution_number" text COLLATE "pg_catalog"."default",
  "resident_physician" text COLLATE "pg_catalog"."default",
  "responsible_nurse_execution_number" text COLLATE "pg_catalog"."default",
  "responsible_nurse" text COLLATE "pg_catalog"."default",
  "visiting_physician" text COLLATE "pg_catalog"."default",
  "intern_physician" text COLLATE "pg_catalog"."default",
  "coder" text COLLATE "pg_catalog"."default",
  "medical_record_quality" text COLLATE "pg_catalog"."default",
  "quality_control_physician" text COLLATE "pg_catalog"."default",
  "quality_control_nurse" text COLLATE "pg_catalog"."default",
  "quality_control_date" text COLLATE "pg_catalog"."default",
  "quality_control_nurse_execution_code" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."hospitalization_record"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."hospitalization_record"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."hospitalization_record"."record_id" IS '病案号/住院号/门诊号';
COMMENT ON COLUMN "public"."hospitalization_record"."organization_code" IS '组织机构代码';
COMMENT ON COLUMN "public"."hospitalization_record"."medical_institution_name" IS '医疗机构名称';
COMMENT ON COLUMN "public"."hospitalization_record"."hospitalization_count" IS '住院次数';
COMMENT ON COLUMN "public"."hospitalization_record"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."hospitalization_record"."discharge_time" IS '出院时间';
COMMENT ON COLUMN "public"."hospitalization_record"."payment_method" IS '医疗付费方式';
COMMENT ON COLUMN "public"."hospitalization_record"."is_day_surgery" IS '是否为日间手术';
COMMENT ON COLUMN "public"."hospitalization_record"."admission_path" IS '入院途径';
COMMENT ON COLUMN "public"."hospitalization_record"."admission_department" IS '入院科别';
COMMENT ON COLUMN "public"."hospitalization_record"."admission_ward" IS '入院病房';
COMMENT ON COLUMN "public"."hospitalization_record"."transfer_department" IS '转科科别';
COMMENT ON COLUMN "public"."hospitalization_record"."discharge_department" IS '出院科别';
COMMENT ON COLUMN "public"."hospitalization_record"."discharge_ward" IS '出院病房';
COMMENT ON COLUMN "public"."hospitalization_record"."actual_hospitalization_days" IS '实际住院（天）';
COMMENT ON COLUMN "public"."hospitalization_record"."has_rehospitalization_plan" IS '是否有出院31日内再住院计划';
COMMENT ON COLUMN "public"."hospitalization_record"."rehospitalization_plan_purpose" IS '出院31天再住院计划目的';
COMMENT ON COLUMN "public"."hospitalization_record"."discharge_method" IS '离院方式';
COMMENT ON COLUMN "public"."hospitalization_record"."transfer_institution_name" IS '医嘱转院、转社区卫生服务机构/乡镇卫生院名称';
COMMENT ON COLUMN "public"."hospitalization_record"."admission_condition" IS '入院时情况';
COMMENT ON COLUMN "public"."hospitalization_record"."emergency_diagnosis_code" IS '门（急）诊诊断编码';
COMMENT ON COLUMN "public"."hospitalization_record"."emergency_diagnosis_name" IS '门（急）诊诊断名称';
COMMENT ON COLUMN "public"."hospitalization_record"."death_patient_autopsy" IS '死亡患者尸检';
COMMENT ON COLUMN "public"."hospitalization_record"."age_under_one_week_days" IS '年龄不足1周岁的年龄（天）';
COMMENT ON COLUMN "public"."hospitalization_record"."newborn_birth_weights" IS '新生儿出生体重（克）数组，最多5个';
COMMENT ON COLUMN "public"."hospitalization_record"."newborn_admission_weight" IS '新生儿入院体重（克）';
COMMENT ON COLUMN "public"."hospitalization_record"."craniocerebral_injury_before_coma_days" IS '颅脑损伤患者入院前昏迷时间（天）';
COMMENT ON COLUMN "public"."hospitalization_record"."craniocerebral_injury_before_coma_hours" IS '颅脑损伤患者入院前昏迷时间（小时）';
COMMENT ON COLUMN "public"."hospitalization_record"."craniocerebral_injury_before_coma_minutes" IS '颅脑损伤患者入院前昏迷时间（分钟）';
COMMENT ON COLUMN "public"."hospitalization_record"."craniocerebral_injury_after_coma_days" IS '颅脑损伤患者入院后昏迷时间（天）';
COMMENT ON COLUMN "public"."hospitalization_record"."craniocerebral_injury_after_coma_hours" IS '颅脑损伤患者入院后昏迷时间（小时）';
COMMENT ON COLUMN "public"."hospitalization_record"."craniocerebral_injury_after_coma_minutes" IS '颅脑损伤患者入院后昏迷时间（分钟）';
COMMENT ON COLUMN "public"."hospitalization_record"."special_care_days" IS '特级护理天数';
COMMENT ON COLUMN "public"."hospitalization_record"."first_level_care_days" IS '一级护理天数';
COMMENT ON COLUMN "public"."hospitalization_record"."second_level_care_days" IS '二级护理天数';
COMMENT ON COLUMN "public"."hospitalization_record"."third_level_care_days" IS '三级护理天数';
COMMENT ON COLUMN "public"."hospitalization_record"."blood_transfusion_reaction" IS '输血反应';
COMMENT ON COLUMN "public"."hospitalization_record"."red_blood_cells" IS '红细胞';
COMMENT ON COLUMN "public"."hospitalization_record"."platelets" IS '血小板';
COMMENT ON COLUMN "public"."hospitalization_record"."plasma" IS '血浆';
COMMENT ON COLUMN "public"."hospitalization_record"."whole_blood" IS '全血';
COMMENT ON COLUMN "public"."hospitalization_record"."autologous_blood_transfusion" IS '自体血回输';
COMMENT ON COLUMN "public"."hospitalization_record"."invasive_ventilator_time" IS '有创呼吸机使用时间';
COMMENT ON COLUMN "public"."hospitalization_record"."icu_names" IS '重症监护室名称数组';
COMMENT ON COLUMN "public"."hospitalization_record"."icu_entry_times" IS '重症监护室进入时间数组';
COMMENT ON COLUMN "public"."hospitalization_record"."icu_exit_times" IS '重症监护室退出时间数组';
COMMENT ON COLUMN "public"."hospitalization_record"."department_director_execution_number" IS '科主任执行编号';
COMMENT ON COLUMN "public"."hospitalization_record"."department_director" IS '科主任';
COMMENT ON COLUMN "public"."hospitalization_record"."chief_physician_execution_number" IS '主（副主）任医师执行编号';
COMMENT ON COLUMN "public"."hospitalization_record"."chief_physician" IS '主（副主）任医师';
COMMENT ON COLUMN "public"."hospitalization_record"."attending_physician_execution_number" IS '主治医师执行编号';
COMMENT ON COLUMN "public"."hospitalization_record"."attending_physician" IS '主治医师';
COMMENT ON COLUMN "public"."hospitalization_record"."resident_physician_execution_number" IS '住院医师执行编号';
COMMENT ON COLUMN "public"."hospitalization_record"."resident_physician" IS '住院医师';
COMMENT ON COLUMN "public"."hospitalization_record"."responsible_nurse_execution_number" IS '责任护士执行编号';
COMMENT ON COLUMN "public"."hospitalization_record"."responsible_nurse" IS '责任护士';
COMMENT ON COLUMN "public"."hospitalization_record"."visiting_physician" IS '进修医师';
COMMENT ON COLUMN "public"."hospitalization_record"."intern_physician" IS '实习医师';
COMMENT ON COLUMN "public"."hospitalization_record"."coder" IS '编码员';
COMMENT ON COLUMN "public"."hospitalization_record"."medical_record_quality" IS '病案质量';
COMMENT ON COLUMN "public"."hospitalization_record"."quality_control_physician" IS '质控医师';
COMMENT ON COLUMN "public"."hospitalization_record"."quality_control_nurse" IS '质控护师';
COMMENT ON COLUMN "public"."hospitalization_record"."quality_control_date" IS '质控日期';
COMMENT ON COLUMN "public"."hospitalization_record"."quality_control_nurse_execution_code" IS '质控护士执行代码';
COMMENT ON TABLE "public"."hospitalization_record" IS '住院记录表';

-- ----------------------------
-- Table structure for icu_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."icu_record";
CREATE TABLE "public"."icu_record" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "admission_time" date NOT NULL,
  "icu_seq" int4 NOT NULL,
  "icu_name" text COLLATE "pg_catalog"."default",
  "icu_enter_time" timestamp(6),
  "icu_exit_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."icu_record"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."icu_record"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."icu_record"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."icu_record"."icu_seq" IS '重症监护序号';
COMMENT ON COLUMN "public"."icu_record"."icu_name" IS '重症监护室名称';
COMMENT ON COLUMN "public"."icu_record"."icu_enter_time" IS '重症监护室进入时间';
COMMENT ON COLUMN "public"."icu_record"."icu_exit_time" IS '重症监护室退出时间';
COMMENT ON TABLE "public"."icu_record" IS '重症监护记录表';

-- ----------------------------
-- Table structure for imaging_examination_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."imaging_examination_record";
CREATE TABLE "public"."imaging_examination_record" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "unique_id" text COLLATE "pg_catalog"."default",
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "patient_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "age" int4 NOT NULL,
  "gender" text COLLATE "pg_catalog"."default" NOT NULL,
  "birth_date" date NOT NULL,
  "admission_time" date NOT NULL,
  "discharge_time" date,
  "examination_no" text COLLATE "pg_catalog"."default" NOT NULL,
  "device_type" text COLLATE "pg_catalog"."default" NOT NULL,
  "examination_item" text COLLATE "pg_catalog"."default" NOT NULL,
  "examination_part" text COLLATE "pg_catalog"."default" NOT NULL,
  "examination_time" timestamp(6) NOT NULL,
  "imaging_finding" text COLLATE "pg_catalog"."default",
  "conclusion" text COLLATE "pg_catalog"."default" NOT NULL,
  "referring_department" text COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."imaging_examination_record"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."imaging_examination_record"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."imaging_examination_record"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."imaging_examination_record"."patient_name" IS '姓名';
COMMENT ON COLUMN "public"."imaging_examination_record"."age" IS '年龄';
COMMENT ON COLUMN "public"."imaging_examination_record"."gender" IS '性别';
COMMENT ON COLUMN "public"."imaging_examination_record"."birth_date" IS '出生日期';
COMMENT ON COLUMN "public"."imaging_examination_record"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."imaging_examination_record"."discharge_time" IS '出院时间';
COMMENT ON COLUMN "public"."imaging_examination_record"."examination_no" IS '检查号';
COMMENT ON COLUMN "public"."imaging_examination_record"."device_type" IS '检查设备类型';
COMMENT ON COLUMN "public"."imaging_examination_record"."examination_item" IS '检查项目';
COMMENT ON COLUMN "public"."imaging_examination_record"."examination_part" IS '检查部位';
COMMENT ON COLUMN "public"."imaging_examination_record"."examination_time" IS '检查时间';
COMMENT ON COLUMN "public"."imaging_examination_record"."imaging_finding" IS '影像所见';
COMMENT ON COLUMN "public"."imaging_examination_record"."conclusion" IS '检查结论';
COMMENT ON COLUMN "public"."imaging_examination_record"."referring_department" IS '送检科室名称';
COMMENT ON TABLE "public"."imaging_examination_record" IS '影像学检查数据表';

-- ----------------------------
-- Table structure for laboratory_test_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."laboratory_test_record";
CREATE TABLE "public"."laboratory_test_record" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "unique_id" text COLLATE "pg_catalog"."default",
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "patient_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "age" int4 NOT NULL,
  "gender" text COLLATE "pg_catalog"."default" NOT NULL,
  "birth_date" date NOT NULL,
  "admission_time" date NOT NULL,
  "discharge_time" date,
  "report_date" timestamp(6) NOT NULL,
  "test_item" text COLLATE "pg_catalog"."default" NOT NULL,
  "indicator_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "english_name" text COLLATE "pg_catalog"."default",
  "indicator_value" text COLLATE "pg_catalog"."default" NOT NULL,
  "unit" text COLLATE "pg_catalog"."default",
  "reference_range" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."laboratory_test_record"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."laboratory_test_record"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."laboratory_test_record"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."laboratory_test_record"."patient_name" IS '姓名';
COMMENT ON COLUMN "public"."laboratory_test_record"."age" IS '年龄';
COMMENT ON COLUMN "public"."laboratory_test_record"."gender" IS '性别';
COMMENT ON COLUMN "public"."laboratory_test_record"."birth_date" IS '出生日期';
COMMENT ON COLUMN "public"."laboratory_test_record"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."laboratory_test_record"."discharge_time" IS '出院时间';
COMMENT ON COLUMN "public"."laboratory_test_record"."report_date" IS '报告日期';
COMMENT ON COLUMN "public"."laboratory_test_record"."test_item" IS '检验项目';
COMMENT ON COLUMN "public"."laboratory_test_record"."indicator_name" IS '检验指标名';
COMMENT ON COLUMN "public"."laboratory_test_record"."english_name" IS '英文名称';
COMMENT ON COLUMN "public"."laboratory_test_record"."indicator_value" IS '检验指标值';
COMMENT ON COLUMN "public"."laboratory_test_record"."unit" IS '单位';
COMMENT ON COLUMN "public"."laboratory_test_record"."reference_range" IS '参考范围';
COMMENT ON TABLE "public"."laboratory_test_record" IS '实验室检查数据表';

-- ----------------------------
-- Table structure for medical_cost_detail
-- ----------------------------
DROP TABLE IF EXISTS "public"."medical_cost_detail";
CREATE TABLE "public"."medical_cost_detail" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "admission_time" date NOT NULL,
  "cost_type" text COLLATE "pg_catalog"."default" NOT NULL,
  "cost_amount" numeric(12,2)
)
;
COMMENT ON COLUMN "public"."medical_cost_detail"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."medical_cost_detail"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."medical_cost_detail"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."medical_cost_detail"."cost_type" IS '费用类型';
COMMENT ON COLUMN "public"."medical_cost_detail"."cost_amount" IS '费用金额';
COMMENT ON TABLE "public"."medical_cost_detail" IS '患者费用明细表';

-- ----------------------------
-- Table structure for medical_order
-- ----------------------------
DROP TABLE IF EXISTS "public"."medical_order";
CREATE TABLE "public"."medical_order" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "unique_id" text COLLATE "pg_catalog"."default",
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "patient_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "age" int4 NOT NULL,
  "gender" text COLLATE "pg_catalog"."default" NOT NULL,
  "birth_date" date NOT NULL,
  "admission_time" date NOT NULL,
  "discharge_time" date,
  "start_time" timestamp(6) NOT NULL,
  "end_time" timestamp(6),
  "order_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "frequency" text COLLATE "pg_catalog"."default",
  "usage_method" text COLLATE "pg_catalog"."default",
  "dosage" float8
)
;
COMMENT ON COLUMN "public"."medical_order"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."medical_order"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."medical_order"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."medical_order"."patient_name" IS '姓名';
COMMENT ON COLUMN "public"."medical_order"."age" IS '年龄';
COMMENT ON COLUMN "public"."medical_order"."gender" IS '性别';
COMMENT ON COLUMN "public"."medical_order"."birth_date" IS '出生日期';
COMMENT ON COLUMN "public"."medical_order"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."medical_order"."discharge_time" IS '出院时间';
COMMENT ON COLUMN "public"."medical_order"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."medical_order"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."medical_order"."order_name" IS '医嘱名称';
COMMENT ON COLUMN "public"."medical_order"."frequency" IS '频次';
COMMENT ON COLUMN "public"."medical_order"."usage_method" IS '用法';
COMMENT ON COLUMN "public"."medical_order"."dosage" IS '剂量';
COMMENT ON TABLE "public"."medical_order" IS '医嘱数据表';

-- ----------------------------
-- Table structure for medical_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."medical_record";
CREATE TABLE "public"."medical_record" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "admission_time" date NOT NULL,
  "discharge_time" date NOT NULL,
  "department_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "department_name" text COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."medical_record"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."medical_record"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."medical_record"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."medical_record"."discharge_time" IS '出院时间';
COMMENT ON COLUMN "public"."medical_record"."department_id" IS '就诊科室号';
COMMENT ON COLUMN "public"."medical_record"."department_name" IS '就诊科室名称';
COMMENT ON TABLE "public"."medical_record" IS '患者就诊记录表';

-- ----------------------------
-- Table structure for medical_record_front_page
-- ----------------------------
DROP TABLE IF EXISTS "public"."medical_record_front_page";
CREATE TABLE "public"."medical_record_front_page" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "unique_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "patient_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "age" int4 NOT NULL,
  "gender" text COLLATE "pg_catalog"."default" NOT NULL,
  "birth_date" date NOT NULL,
  "admission_time" date NOT NULL,
  "discharge_time" date NOT NULL,
  "organization_code" text COLLATE "pg_catalog"."default",
  "hospital_name" text COLLATE "pg_catalog"."default",
  "medical_record_number" text COLLATE "pg_catalog"."default",
  "hospitalization_number" int4,
  "payment_method" text COLLATE "pg_catalog"."default",
  "nationality" text COLLATE "pg_catalog"."default",
  "marital_status" text COLLATE "pg_catalog"."default",
  "occupation" text COLLATE "pg_catalog"."default",
  "ethnicity" text COLLATE "pg_catalog"."default",
  "id_type" text COLLATE "pg_catalog"."default",
  "id_number" text COLLATE "pg_catalog"."default",
  "birth_address" text COLLATE "pg_catalog"."default",
  "registered_residence" text COLLATE "pg_catalog"."default",
  "registered_residence_postcode" text COLLATE "pg_catalog"."default",
  "current_address" text COLLATE "pg_catalog"."default",
  "current_address_phone" text COLLATE "pg_catalog"."default",
  "current_address_postcode" text COLLATE "pg_catalog"."default",
  "work_unit_address" text COLLATE "pg_catalog"."default",
  "work_unit_phone" text COLLATE "pg_catalog"."default",
  "work_unit_postcode" text COLLATE "pg_catalog"."default",
  "contact_name" text COLLATE "pg_catalog"."default",
  "contact_relationship" text COLLATE "pg_catalog"."default",
  "contact_address" text COLLATE "pg_catalog"."default",
  "contact_phone" text COLLATE "pg_catalog"."default",
  "is_day_surgery" bool,
  "admission_route" text COLLATE "pg_catalog"."default",
  "admission_department" text COLLATE "pg_catalog"."default",
  "admission_ward" text COLLATE "pg_catalog"."default",
  "transfer_department" text COLLATE "pg_catalog"."default",
  "discharge_department" text COLLATE "pg_catalog"."default",
  "discharge_ward" text COLLATE "pg_catalog"."default",
  "actual_days_in_hospital" int4,
  "emergency_diagnosis_code" text COLLATE "pg_catalog"."default",
  "emergency_diagnosis_name" text COLLATE "pg_catalog"."default",
  "days_for_under_one_year" int4,
  "readmission_plan_in_31_days" bool,
  "readmission_plan_purpose" text COLLATE "pg_catalog"."default",
  "leave_type" text COLLATE "pg_catalog"."default",
  "transfer_hospital_name" text COLLATE "pg_catalog"."default",
  "condition_on_admission" text COLLATE "pg_catalog"."default",
  "admission_diagnosis_code" text COLLATE "pg_catalog"."default",
  "admission_diagnosis_name" text COLLATE "pg_catalog"."default",
  "confirmed_diagnosis_date" date,
  "discharge_main_diagnosis_code" text COLLATE "pg_catalog"."default",
  "discharge_main_diagnosis_name" text COLLATE "pg_catalog"."default",
  "main_diagnosis_condition_on_admission" text COLLATE "pg_catalog"."default",
  "main_diagnosis_condition_on_discharge" text COLLATE "pg_catalog"."default",
  "allergic_drug_flag" bool,
  "allergic_drug_name" text COLLATE "pg_catalog"."default",
  "blood_type_abo" text COLLATE "pg_catalog"."default",
  "blood_type_rh" text COLLATE "pg_catalog"."default",
  "total_cost" numeric(12,2),
  "self_payment" numeric(12,2),
  "director_code" text COLLATE "pg_catalog"."default",
  "director_name" text COLLATE "pg_catalog"."default",
  "attending_doctor_code" text COLLATE "pg_catalog"."default",
  "attending_doctor_name" text COLLATE "pg_catalog"."default",
  "resident_doctor_code" text COLLATE "pg_catalog"."default",
  "resident_doctor_name" text COLLATE "pg_catalog"."default",
  "responsible_nurse_code" text COLLATE "pg_catalog"."default",
  "responsible_nurse_name" text COLLATE "pg_catalog"."default",
  "quality_control_doctor" text COLLATE "pg_catalog"."default",
  "quality_control_nurse" text COLLATE "pg_catalog"."default",
  "quality_control_date" date
)
;
COMMENT ON COLUMN "public"."medical_record_front_page"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."medical_record_front_page"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."medical_record_front_page"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."medical_record_front_page"."patient_name" IS '姓名';
COMMENT ON COLUMN "public"."medical_record_front_page"."age" IS '年龄';
COMMENT ON COLUMN "public"."medical_record_front_page"."gender" IS '性别';
COMMENT ON COLUMN "public"."medical_record_front_page"."birth_date" IS '出生日期';
COMMENT ON COLUMN "public"."medical_record_front_page"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."medical_record_front_page"."discharge_time" IS '出院时间';
COMMENT ON COLUMN "public"."medical_record_front_page"."organization_code" IS '组织机构代码';
COMMENT ON COLUMN "public"."medical_record_front_page"."hospital_name" IS '医疗机构名称';
COMMENT ON COLUMN "public"."medical_record_front_page"."medical_record_number" IS '病案号';
COMMENT ON COLUMN "public"."medical_record_front_page"."hospitalization_number" IS '住院次数';
COMMENT ON COLUMN "public"."medical_record_front_page"."payment_method" IS '医疗付费方式';
COMMENT ON COLUMN "public"."medical_record_front_page"."nationality" IS '国籍';
COMMENT ON COLUMN "public"."medical_record_front_page"."marital_status" IS '婚姻';
COMMENT ON COLUMN "public"."medical_record_front_page"."occupation" IS '职业';
COMMENT ON COLUMN "public"."medical_record_front_page"."ethnicity" IS '民族';
COMMENT ON COLUMN "public"."medical_record_front_page"."id_type" IS '证件类别';
COMMENT ON COLUMN "public"."medical_record_front_page"."id_number" IS '身份证号';
COMMENT ON COLUMN "public"."medical_record_front_page"."birth_address" IS '出生地址';
COMMENT ON COLUMN "public"."medical_record_front_page"."registered_residence" IS '户口地址';
COMMENT ON COLUMN "public"."medical_record_front_page"."registered_residence_postcode" IS '户口地址邮政编码';
COMMENT ON COLUMN "public"."medical_record_front_page"."current_address" IS '现住址';
COMMENT ON COLUMN "public"."medical_record_front_page"."current_address_phone" IS '现住址电话';
COMMENT ON COLUMN "public"."medical_record_front_page"."current_address_postcode" IS '现住址邮政编码';
COMMENT ON COLUMN "public"."medical_record_front_page"."work_unit_address" IS '工作单位及地址';
COMMENT ON COLUMN "public"."medical_record_front_page"."work_unit_phone" IS '工作单位电话';
COMMENT ON COLUMN "public"."medical_record_front_page"."work_unit_postcode" IS '工作单位邮政编码';
COMMENT ON COLUMN "public"."medical_record_front_page"."contact_name" IS '联系人姓名';
COMMENT ON COLUMN "public"."medical_record_front_page"."contact_relationship" IS '联系人关系';
COMMENT ON COLUMN "public"."medical_record_front_page"."contact_address" IS '联系人地址';
COMMENT ON COLUMN "public"."medical_record_front_page"."contact_phone" IS '联系人电话';
COMMENT ON COLUMN "public"."medical_record_front_page"."is_day_surgery" IS '是否为日间手术';
COMMENT ON COLUMN "public"."medical_record_front_page"."admission_route" IS '入院途径';
COMMENT ON COLUMN "public"."medical_record_front_page"."admission_department" IS '入院科别';
COMMENT ON COLUMN "public"."medical_record_front_page"."admission_ward" IS '入院病房';
COMMENT ON COLUMN "public"."medical_record_front_page"."transfer_department" IS '转科科别';
COMMENT ON COLUMN "public"."medical_record_front_page"."discharge_department" IS '出院科别';
COMMENT ON COLUMN "public"."medical_record_front_page"."discharge_ward" IS '出院病房';
COMMENT ON COLUMN "public"."medical_record_front_page"."actual_days_in_hospital" IS '实际住院（天）';
COMMENT ON COLUMN "public"."medical_record_front_page"."emergency_diagnosis_code" IS '门（急）诊诊断编码';
COMMENT ON COLUMN "public"."medical_record_front_page"."emergency_diagnosis_name" IS '门（急）诊诊断名称';
COMMENT ON COLUMN "public"."medical_record_front_page"."days_for_under_one_year" IS '年龄不足1周岁的年龄（天）';
COMMENT ON COLUMN "public"."medical_record_front_page"."readmission_plan_in_31_days" IS '是否有出院31日内再住院计划';
COMMENT ON COLUMN "public"."medical_record_front_page"."readmission_plan_purpose" IS '出院31天再住院计划目的';
COMMENT ON COLUMN "public"."medical_record_front_page"."leave_type" IS '离院方式';
COMMENT ON COLUMN "public"."medical_record_front_page"."transfer_hospital_name" IS '医嘱转院、转社区卫生服务机构/乡镇卫生院名称';
COMMENT ON COLUMN "public"."medical_record_front_page"."condition_on_admission" IS '入院时情况';
COMMENT ON COLUMN "public"."medical_record_front_page"."admission_diagnosis_code" IS '入院诊断编码';
COMMENT ON COLUMN "public"."medical_record_front_page"."admission_diagnosis_name" IS '入院诊断名称';
COMMENT ON COLUMN "public"."medical_record_front_page"."confirmed_diagnosis_date" IS '入院后确诊日期';
COMMENT ON COLUMN "public"."medical_record_front_page"."discharge_main_diagnosis_code" IS '出院主要诊断编码';
COMMENT ON COLUMN "public"."medical_record_front_page"."discharge_main_diagnosis_name" IS '出院主要诊断名称';
COMMENT ON COLUMN "public"."medical_record_front_page"."main_diagnosis_condition_on_admission" IS '主要诊断入院病情';
COMMENT ON COLUMN "public"."medical_record_front_page"."main_diagnosis_condition_on_discharge" IS '主要诊断出院情况';
COMMENT ON COLUMN "public"."medical_record_front_page"."allergic_drug_flag" IS '有无药物过敏';
COMMENT ON COLUMN "public"."medical_record_front_page"."allergic_drug_name" IS '过敏药物名称';
COMMENT ON COLUMN "public"."medical_record_front_page"."blood_type_abo" IS 'ABO血型';
COMMENT ON COLUMN "public"."medical_record_front_page"."blood_type_rh" IS 'Rh血型';
COMMENT ON COLUMN "public"."medical_record_front_page"."total_cost" IS '住院总费用';
COMMENT ON COLUMN "public"."medical_record_front_page"."self_payment" IS '住院总费用其中自付金额';
COMMENT ON COLUMN "public"."medical_record_front_page"."director_code" IS '科主任执行编号';
COMMENT ON COLUMN "public"."medical_record_front_page"."director_name" IS '科主任';
COMMENT ON COLUMN "public"."medical_record_front_page"."attending_doctor_code" IS '主（副主）任医师执行编号';
COMMENT ON COLUMN "public"."medical_record_front_page"."attending_doctor_name" IS '主（副主）任医师';
COMMENT ON COLUMN "public"."medical_record_front_page"."resident_doctor_code" IS '住院医师执行编号';
COMMENT ON COLUMN "public"."medical_record_front_page"."resident_doctor_name" IS '住院医师';
COMMENT ON COLUMN "public"."medical_record_front_page"."responsible_nurse_code" IS '责任护士执行编号';
COMMENT ON COLUMN "public"."medical_record_front_page"."responsible_nurse_name" IS '责任护士';
COMMENT ON COLUMN "public"."medical_record_front_page"."quality_control_doctor" IS '质控医师';
COMMENT ON COLUMN "public"."medical_record_front_page"."quality_control_nurse" IS '质控护师';
COMMENT ON COLUMN "public"."medical_record_front_page"."quality_control_date" IS '质控日期';
COMMENT ON TABLE "public"."medical_record_front_page" IS '患者病案首页表';

-- ----------------------------
-- Table structure for nursing_level
-- ----------------------------
DROP TABLE IF EXISTS "public"."nursing_level";
CREATE TABLE "public"."nursing_level" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "admission_time" date NOT NULL,
  "nursing_level_type" text COLLATE "pg_catalog"."default" NOT NULL,
  "days" int4
)
;
COMMENT ON COLUMN "public"."nursing_level"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."nursing_level"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."nursing_level"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."nursing_level"."nursing_level_type" IS '护理等级类型';
COMMENT ON COLUMN "public"."nursing_level"."days" IS '天数';
COMMENT ON TABLE "public"."nursing_level" IS '护理等级记录表';

-- ----------------------------
-- Table structure for nursing_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."nursing_record";
CREATE TABLE "public"."nursing_record" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "unique_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "admission_time" date NOT NULL,
  "discharge_time" date NOT NULL,
  "project_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "project_code" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_time" timestamp(6) NOT NULL,
  "project_value_char" text COLLATE "pg_catalog"."default",
  "project_value_num" text COLLATE "pg_catalog"."default",
  "measurement_unit" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."nursing_record"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."nursing_record"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."nursing_record"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."nursing_record"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."nursing_record"."discharge_time" IS '出院时间';
COMMENT ON COLUMN "public"."nursing_record"."project_name" IS '项目名称';
COMMENT ON COLUMN "public"."nursing_record"."project_code" IS '项目代号';
COMMENT ON COLUMN "public"."nursing_record"."record_time" IS '记录时间';
COMMENT ON COLUMN "public"."nursing_record"."project_value_char" IS '项目值(字符)';
COMMENT ON COLUMN "public"."nursing_record"."project_value_num" IS '项目值(数值)';
COMMENT ON COLUMN "public"."nursing_record"."measurement_unit" IS '测量单位';
COMMENT ON TABLE "public"."nursing_record" IS '患者护理记录表';

-- ----------------------------
-- Table structure for operation_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."operation_info";
CREATE TABLE "public"."operation_info" (
  "link_id" text COLLATE "pg_catalog"."default",
  "unique_id" text COLLATE "pg_catalog"."default",
  "record_id" text COLLATE "pg_catalog"."default",
  "main_operation_code" text COLLATE "pg_catalog"."default",
  "main_operation_name" text COLLATE "pg_catalog"."default",
  "main_operation_date" text COLLATE "pg_catalog"."default",
  "main_operation_level" text COLLATE "pg_catalog"."default",
  "main_operation_duration" text COLLATE "pg_catalog"."default",
  "main_operation_surgeon" text COLLATE "pg_catalog"."default",
  "main_operation_first_assistant" text COLLATE "pg_catalog"."default",
  "main_operation_second_assistant" text COLLATE "pg_catalog"."default",
  "main_operation_wound_healing_grade" text COLLATE "pg_catalog"."default",
  "main_operation_anesthesia_method" text COLLATE "pg_catalog"."default",
  "main_operation_anesthesia_level" text COLLATE "pg_catalog"."default",
  "main_operation_anesthesiologist" text COLLATE "pg_catalog"."default",
  "other_operation_codes" text[] COLLATE "pg_catalog"."default",
  "other_operation_names" text[] COLLATE "pg_catalog"."default",
  "other_operation_dates" text[] COLLATE "pg_catalog"."default",
  "other_operation_levels" text[] COLLATE "pg_catalog"."default",
  "other_operation_durations" text[] COLLATE "pg_catalog"."default",
  "other_operation_surgeons" text[] COLLATE "pg_catalog"."default",
  "other_operation_first_assistants" text[] COLLATE "pg_catalog"."default",
  "other_operation_second_assistants" text[] COLLATE "pg_catalog"."default",
  "other_operation_wound_healing_grades" text[] COLLATE "pg_catalog"."default",
  "other_operation_anesthesia_methods" text[] COLLATE "pg_catalog"."default",
  "other_operation_anesthesia_levels" text[] COLLATE "pg_catalog"."default",
  "other_operation_anesthesiologists" text[] COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."operation_info"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."operation_info"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."operation_info"."record_id" IS '病案号/住院号/门诊号';
COMMENT ON COLUMN "public"."operation_info"."main_operation_code" IS '主要手术操作编码';
COMMENT ON COLUMN "public"."operation_info"."main_operation_name" IS '主要手术操作名称';
COMMENT ON COLUMN "public"."operation_info"."main_operation_date" IS '主要手术操作日期';
COMMENT ON COLUMN "public"."operation_info"."main_operation_level" IS '主要手术操作级别';
COMMENT ON COLUMN "public"."operation_info"."main_operation_duration" IS '主要手术操作持续时间';
COMMENT ON COLUMN "public"."operation_info"."main_operation_surgeon" IS '主要手术操作术者';
COMMENT ON COLUMN "public"."operation_info"."main_operation_first_assistant" IS '主要手术操作Ⅰ助';
COMMENT ON COLUMN "public"."operation_info"."main_operation_second_assistant" IS '主要手术操作Ⅱ助';
COMMENT ON COLUMN "public"."operation_info"."main_operation_wound_healing_grade" IS '主要手术操作切口愈合等级';
COMMENT ON COLUMN "public"."operation_info"."main_operation_anesthesia_method" IS '主要手术操作麻醉方式';
COMMENT ON COLUMN "public"."operation_info"."main_operation_anesthesia_level" IS '主要手术操作麻醉分级';
COMMENT ON COLUMN "public"."operation_info"."main_operation_anesthesiologist" IS '主要手术操作麻醉医师';
COMMENT ON COLUMN "public"."operation_info"."other_operation_codes" IS '其他手术操作编码数组';
COMMENT ON COLUMN "public"."operation_info"."other_operation_names" IS '其他手术操作名称数组';
COMMENT ON COLUMN "public"."operation_info"."other_operation_dates" IS '其他手术操作日期数组';
COMMENT ON COLUMN "public"."operation_info"."other_operation_levels" IS '其他手术操作级别数组';
COMMENT ON COLUMN "public"."operation_info"."other_operation_durations" IS '其他手术操作持续时间数组';
COMMENT ON COLUMN "public"."operation_info"."other_operation_surgeons" IS '其他手术操作术者数组';
COMMENT ON COLUMN "public"."operation_info"."other_operation_first_assistants" IS '其他手术操作Ⅰ助数组';
COMMENT ON COLUMN "public"."operation_info"."other_operation_second_assistants" IS '其他手术操作Ⅱ助数组';
COMMENT ON COLUMN "public"."operation_info"."other_operation_wound_healing_grades" IS '其他手术操作切口愈合等级数组';
COMMENT ON COLUMN "public"."operation_info"."other_operation_anesthesia_methods" IS '其他手术操作麻醉方式数组';
COMMENT ON COLUMN "public"."operation_info"."other_operation_anesthesia_levels" IS '其他手术操作麻醉分级数组';
COMMENT ON COLUMN "public"."operation_info"."other_operation_anesthesiologists" IS '其他手术操作麻醉医师数组';
COMMENT ON TABLE "public"."operation_info" IS '手术操作表';

-- ----------------------------
-- Table structure for patient_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."patient_base";
CREATE TABLE "public"."patient_base" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "unique_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "patient_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "age" int4 NOT NULL,
  "gender" text COLLATE "pg_catalog"."default" NOT NULL,
  "birth_date" date NOT NULL,
  "sid_card" text COLLATE "pg_catalog"."default",
  "phone_number" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."patient_base"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."patient_base"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."patient_base"."patient_name" IS '姓名';
COMMENT ON COLUMN "public"."patient_base"."age" IS '年龄';
COMMENT ON COLUMN "public"."patient_base"."gender" IS '性别';
COMMENT ON COLUMN "public"."patient_base"."birth_date" IS '出生日期';
COMMENT ON COLUMN "public"."patient_base"."sid_card" IS '身份证号码';
COMMENT ON COLUMN "public"."patient_base"."phone_number" IS '联系方式';
COMMENT ON TABLE "public"."patient_base" IS '患者基本信息表';

-- ----------------------------
-- Table structure for patient_basic_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."patient_basic_info";
CREATE TABLE "public"."patient_basic_info" (
  "link_id" text COLLATE "pg_catalog"."default",
  "unique_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default",
  "health_card_number" text COLLATE "pg_catalog"."default",
  "name" text COLLATE "pg_catalog"."default",
  "gender" text COLLATE "pg_catalog"."default",
  "age" text COLLATE "pg_catalog"."default",
  "birth_date" text COLLATE "pg_catalog"."default",
  "nationality" text COLLATE "pg_catalog"."default",
  "marital_status" text COLLATE "pg_catalog"."default",
  "occupation" text COLLATE "pg_catalog"."default",
  "ethnicity" text COLLATE "pg_catalog"."default",
  "id_type" text COLLATE "pg_catalog"."default",
  "id_number" text COLLATE "pg_catalog"."default",
  "birth_address" text COLLATE "pg_catalog"."default",
  "native_province" text COLLATE "pg_catalog"."default",
  "household_address" text COLLATE "pg_catalog"."default",
  "household_postal_code" text COLLATE "pg_catalog"."default",
  "current_address" text COLLATE "pg_catalog"."default",
  "current_address_phone" text COLLATE "pg_catalog"."default",
  "current_address_postal_code" text COLLATE "pg_catalog"."default",
  "work_unit_address" text COLLATE "pg_catalog"."default",
  "work_unit_phone" text COLLATE "pg_catalog"."default",
  "work_unit_postal_code" text COLLATE "pg_catalog"."default",
  "contact_name" text COLLATE "pg_catalog"."default",
  "contact_relationship" text COLLATE "pg_catalog"."default",
  "contact_address" text COLLATE "pg_catalog"."default",
  "contact_phone" text COLLATE "pg_catalog"."default",
  "has_drug_allergy" text COLLATE "pg_catalog"."default",
  "allergic_drug_name" text COLLATE "pg_catalog"."default",
  "hbsag" text COLLATE "pg_catalog"."default",
  "hcv_ab" text COLLATE "pg_catalog"."default",
  "hiv_ab" text COLLATE "pg_catalog"."default",
  "abo_blood_type" text COLLATE "pg_catalog"."default",
  "rh_blood_type" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."patient_basic_info"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."patient_basic_info"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."patient_basic_info"."record_id" IS '病案号/住院号/门诊号';
COMMENT ON COLUMN "public"."patient_basic_info"."health_card_number" IS '健康卡号';
COMMENT ON COLUMN "public"."patient_basic_info"."name" IS '姓名';
COMMENT ON COLUMN "public"."patient_basic_info"."gender" IS '性别';
COMMENT ON COLUMN "public"."patient_basic_info"."age" IS '年龄';
COMMENT ON COLUMN "public"."patient_basic_info"."birth_date" IS '出生日期';
COMMENT ON COLUMN "public"."patient_basic_info"."nationality" IS '国籍';
COMMENT ON COLUMN "public"."patient_basic_info"."marital_status" IS '婚姻';
COMMENT ON COLUMN "public"."patient_basic_info"."occupation" IS '职业';
COMMENT ON COLUMN "public"."patient_basic_info"."ethnicity" IS '民族';
COMMENT ON COLUMN "public"."patient_basic_info"."id_type" IS '证件类别';
COMMENT ON COLUMN "public"."patient_basic_info"."id_number" IS '身份证号';
COMMENT ON COLUMN "public"."patient_basic_info"."birth_address" IS '出生地址';
COMMENT ON COLUMN "public"."patient_basic_info"."native_province" IS '籍贯省（自治区、直辖市）';
COMMENT ON COLUMN "public"."patient_basic_info"."household_address" IS '户口地址';
COMMENT ON COLUMN "public"."patient_basic_info"."household_postal_code" IS '户口地址邮政编码';
COMMENT ON COLUMN "public"."patient_basic_info"."current_address" IS '现住址';
COMMENT ON COLUMN "public"."patient_basic_info"."current_address_phone" IS '现住址电话';
COMMENT ON COLUMN "public"."patient_basic_info"."current_address_postal_code" IS '现住址邮政编码';
COMMENT ON COLUMN "public"."patient_basic_info"."work_unit_address" IS '工作单位及地址';
COMMENT ON COLUMN "public"."patient_basic_info"."work_unit_phone" IS '工作单位电话';
COMMENT ON COLUMN "public"."patient_basic_info"."work_unit_postal_code" IS '工作单位邮政编码';
COMMENT ON COLUMN "public"."patient_basic_info"."contact_name" IS '联系人姓名';
COMMENT ON COLUMN "public"."patient_basic_info"."contact_relationship" IS '联系人关系';
COMMENT ON COLUMN "public"."patient_basic_info"."contact_address" IS '联系人地址';
COMMENT ON COLUMN "public"."patient_basic_info"."contact_phone" IS '联系人电话';
COMMENT ON COLUMN "public"."patient_basic_info"."has_drug_allergy" IS '有无药物过敏';
COMMENT ON COLUMN "public"."patient_basic_info"."allergic_drug_name" IS '过敏药物名称';
COMMENT ON COLUMN "public"."patient_basic_info"."hbsag" IS 'HBsAg';
COMMENT ON COLUMN "public"."patient_basic_info"."hcv_ab" IS 'HCV-Ab';
COMMENT ON COLUMN "public"."patient_basic_info"."hiv_ab" IS 'HIV-Ab';
COMMENT ON COLUMN "public"."patient_basic_info"."abo_blood_type" IS 'ABO血型';
COMMENT ON COLUMN "public"."patient_basic_info"."rh_blood_type" IS 'Rh血型';
COMMENT ON TABLE "public"."patient_basic_info" IS '患者基本信息表';

-- ----------------------------
-- Table structure for patient_blood_gas_results
-- ----------------------------
DROP TABLE IF EXISTS "public"."patient_blood_gas_results";
CREATE TABLE "public"."patient_blood_gas_results" (
  "patient_serial_number" text COLLATE "pg_catalog"."default" NOT NULL,
  "blood_gas_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "test_time" timestamp(6) NOT NULL,
  "AaDpO2" text COLLATE "pg_catalog"."default",
  "AaDpO2,T" text COLLATE "pg_catalog"."default",
  "ABE" text COLLATE "pg_catalog"."default",
  "Anion_gap" text COLLATE "pg_catalog"."default",
  "Blood_Type" text COLLATE "pg_catalog"."default",
  "Ca++" text COLLATE "pg_catalog"."default",
  "Ca(7.4)" text COLLATE "pg_catalog"."default",
  "cBase(B),c" text COLLATE "pg_catalog"."default",
  "cBase(Ecf),c" text COLLATE "pg_catalog"."default",
  "cBase(Ecf,ox)" text COLLATE "pg_catalog"."default",
  "cHCO3-(P,st),c" text COLLATE "pg_catalog"."default",
  "Cl-" text COLLATE "pg_catalog"."default",
  "COHb" text COLLATE "pg_catalog"."default",
  "FiO2" text COLLATE "pg_catalog"."default",
  "Glu" text COLLATE "pg_catalog"."default",
  "HCO3-" text COLLATE "pg_catalog"."default",
  "Hct" text COLLATE "pg_catalog"."default",
  "K+" text COLLATE "pg_catalog"."default",
  "Lac" text COLLATE "pg_catalog"."default",
  "MetHb" text COLLATE "pg_catalog"."default",
  "Na+" text COLLATE "pg_catalog"."default",
  "O2Hb" text COLLATE "pg_catalog"."default",
  "p50(act)" text COLLATE "pg_catalog"."default",
  "pCO2" text COLLATE "pg_catalog"."default",
  "pCO2(T)" text COLLATE "pg_catalog"."default",
  "pH" text COLLATE "pg_catalog"."default",
  "pH(T)" text COLLATE "pg_catalog"."default",
  "pO2" text COLLATE "pg_catalog"."default",
  "pO2(a)/FIO2" text COLLATE "pg_catalog"."default",
  "pO2(T)" text COLLATE "pg_catalog"."default",
  "RHb" text COLLATE "pg_catalog"."default",
  "RI" text COLLATE "pg_catalog"."default",
  "SBC" text COLLATE "pg_catalog"."default",
  "sO2" text COLLATE "pg_catalog"."default",
  "T" text COLLATE "pg_catalog"."default",
  "tCO2(B)" text COLLATE "pg_catalog"."default",
  "tCO2(P)" text COLLATE "pg_catalog"."default",
  "tHb" text COLLATE "pg_catalog"."default",
  "tO2" text COLLATE "pg_catalog"."default",
  "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP
)
;

-- ----------------------------
-- Table structure for patient_diagnosis
-- ----------------------------
DROP TABLE IF EXISTS "public"."patient_diagnosis";
CREATE TABLE "public"."patient_diagnosis" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "admission_time" date NOT NULL,
  "diagnosis_type" text COLLATE "pg_catalog"."default" NOT NULL,
  "diagnosis_seq" int4 NOT NULL,
  "diagnosis_code" text COLLATE "pg_catalog"."default",
  "diagnosis_name" text COLLATE "pg_catalog"."default",
  "condition_on_admission" text COLLATE "pg_catalog"."default",
  "condition_on_discharge" text COLLATE "pg_catalog"."default",
  "pathology_number" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."patient_diagnosis"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."patient_diagnosis"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."patient_diagnosis"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."patient_diagnosis"."diagnosis_type" IS '诊断类型';
COMMENT ON COLUMN "public"."patient_diagnosis"."diagnosis_seq" IS '诊断序号';
COMMENT ON COLUMN "public"."patient_diagnosis"."diagnosis_code" IS '诊断编码';
COMMENT ON COLUMN "public"."patient_diagnosis"."diagnosis_name" IS '诊断名称';
COMMENT ON COLUMN "public"."patient_diagnosis"."condition_on_admission" IS '入院病情';
COMMENT ON COLUMN "public"."patient_diagnosis"."condition_on_discharge" IS '出院情况';
COMMENT ON COLUMN "public"."patient_diagnosis"."pathology_number" IS '病理号';
COMMENT ON TABLE "public"."patient_diagnosis" IS '患者诊断表';

-- ----------------------------
-- Table structure for patient_progress_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."patient_progress_record";
CREATE TABLE "public"."patient_progress_record" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "unique_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "patient_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "age" int4 NOT NULL,
  "gender" text COLLATE "pg_catalog"."default" NOT NULL,
  "birth_date" date NOT NULL,
  "admission_time" date NOT NULL,
  "discharge_time" date NOT NULL,
  "record_date" timestamp(6) NOT NULL,
  "record_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "progress_notes" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."patient_progress_record"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."patient_progress_record"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."patient_progress_record"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."patient_progress_record"."patient_name" IS '姓名';
COMMENT ON COLUMN "public"."patient_progress_record"."age" IS '年龄';
COMMENT ON COLUMN "public"."patient_progress_record"."gender" IS '性别';
COMMENT ON COLUMN "public"."patient_progress_record"."birth_date" IS '出生日期';
COMMENT ON COLUMN "public"."patient_progress_record"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."patient_progress_record"."discharge_time" IS '出院时间';
COMMENT ON COLUMN "public"."patient_progress_record"."record_date" IS '日期';
COMMENT ON COLUMN "public"."patient_progress_record"."record_name" IS '记录名称';
COMMENT ON COLUMN "public"."patient_progress_record"."progress_notes" IS '病程记录';
COMMENT ON TABLE "public"."patient_progress_record" IS '患者病程记录表';

-- ----------------------------
-- Table structure for respiratory_support
-- ----------------------------
DROP TABLE IF EXISTS "public"."respiratory_support";
CREATE TABLE "public"."respiratory_support" (
  "unique_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "dtime" timestamp(6) NOT NULL,
  "name" text COLLATE "pg_catalog"."default" NOT NULL,
  "value" text COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."respiratory_support"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."respiratory_support"."dtime" IS '时间';
COMMENT ON COLUMN "public"."respiratory_support"."name" IS '名称';
COMMENT ON COLUMN "public"."respiratory_support"."value" IS '值';
COMMENT ON TABLE "public"."respiratory_support" IS '呼吸支持';

-- ----------------------------
-- Table structure for surgical_operation
-- ----------------------------
DROP TABLE IF EXISTS "public"."surgical_operation";
CREATE TABLE "public"."surgical_operation" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "admission_time" date NOT NULL,
  "operation_type" text COLLATE "pg_catalog"."default" NOT NULL,
  "operation_seq" int4 NOT NULL,
  "operation_code" text COLLATE "pg_catalog"."default",
  "operation_name" text COLLATE "pg_catalog"."default",
  "operation_date" date,
  "operation_level" text COLLATE "pg_catalog"."default",
  "operation_duration" int4,
  "surgeon" text COLLATE "pg_catalog"."default",
  "first_assistant" text COLLATE "pg_catalog"."default",
  "second_assistant" text COLLATE "pg_catalog"."default",
  "incision_healing_grade" text COLLATE "pg_catalog"."default",
  "anesthesia_method" text COLLATE "pg_catalog"."default",
  "anesthesia_level" text COLLATE "pg_catalog"."default",
  "anesthesiologist" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."surgical_operation"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."surgical_operation"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."surgical_operation"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."surgical_operation"."operation_type" IS '手术类型';
COMMENT ON COLUMN "public"."surgical_operation"."operation_seq" IS '手术序号';
COMMENT ON COLUMN "public"."surgical_operation"."operation_code" IS '手术操作编码';
COMMENT ON COLUMN "public"."surgical_operation"."operation_name" IS '手术操作名称';
COMMENT ON COLUMN "public"."surgical_operation"."operation_date" IS '手术操作日期';
COMMENT ON COLUMN "public"."surgical_operation"."operation_level" IS '手术操作级别';
COMMENT ON COLUMN "public"."surgical_operation"."operation_duration" IS '手术操作持续时间';
COMMENT ON COLUMN "public"."surgical_operation"."surgeon" IS '手术操作术者';
COMMENT ON COLUMN "public"."surgical_operation"."first_assistant" IS '手术操作Ⅰ助';
COMMENT ON COLUMN "public"."surgical_operation"."second_assistant" IS '手术操作Ⅱ助';
COMMENT ON COLUMN "public"."surgical_operation"."incision_healing_grade" IS '手术操作切口愈合等级';
COMMENT ON COLUMN "public"."surgical_operation"."anesthesia_method" IS '手术操作麻醉方式';
COMMENT ON COLUMN "public"."surgical_operation"."anesthesia_level" IS '手术操作麻醉分级';
COMMENT ON COLUMN "public"."surgical_operation"."anesthesiologist" IS '手术操作麻醉医师';
COMMENT ON TABLE "public"."surgical_operation" IS '手术操作表';

-- ----------------------------
-- Table structure for surgical_progress_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."surgical_progress_record";
CREATE TABLE "public"."surgical_progress_record" (
  "link_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "unique_id" text COLLATE "pg_catalog"."default",
  "record_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "patient_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "age" int4 NOT NULL,
  "gender" text COLLATE "pg_catalog"."default" NOT NULL,
  "birth_date" date NOT NULL,
  "admission_time" date NOT NULL,
  "discharge_time" date,
  "surgery_date" text COLLATE "pg_catalog"."default",
  "start_time" text COLLATE "pg_catalog"."default",
  "end_time" text COLLATE "pg_catalog"."default",
  "duration" text COLLATE "pg_catalog"."default",
  "surgery_name" text COLLATE "pg_catalog"."default",
  "surgeon" text COLLATE "pg_catalog"."default",
  "first_assistant" text COLLATE "pg_catalog"."default",
  "second_assistant" text COLLATE "pg_catalog"."default",
  "surgery_notes" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."surgical_progress_record"."link_id" IS '关联号';
COMMENT ON COLUMN "public"."surgical_progress_record"."unique_id" IS '唯一号';
COMMENT ON COLUMN "public"."surgical_progress_record"."record_id" IS '住院号/门诊号';
COMMENT ON COLUMN "public"."surgical_progress_record"."patient_name" IS '姓名';
COMMENT ON COLUMN "public"."surgical_progress_record"."age" IS '年龄';
COMMENT ON COLUMN "public"."surgical_progress_record"."gender" IS '性别';
COMMENT ON COLUMN "public"."surgical_progress_record"."birth_date" IS '出生日期';
COMMENT ON COLUMN "public"."surgical_progress_record"."admission_time" IS '入院时间';
COMMENT ON COLUMN "public"."surgical_progress_record"."discharge_time" IS '出院时间';
COMMENT ON COLUMN "public"."surgical_progress_record"."surgery_date" IS '手术日期';
COMMENT ON COLUMN "public"."surgical_progress_record"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."surgical_progress_record"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."surgical_progress_record"."duration" IS '手术时长';
COMMENT ON COLUMN "public"."surgical_progress_record"."surgery_name" IS '手术名称';
COMMENT ON COLUMN "public"."surgical_progress_record"."surgeon" IS '手术医生';
COMMENT ON COLUMN "public"."surgical_progress_record"."first_assistant" IS '第一助手';
COMMENT ON COLUMN "public"."surgical_progress_record"."second_assistant" IS '第二助手';
COMMENT ON COLUMN "public"."surgical_progress_record"."surgery_notes" IS '手术记录';
COMMENT ON TABLE "public"."surgical_progress_record" IS '患者手术病程记录表';

-- ----------------------------
-- Function structure for uuid_generate_v1
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v1"();
CREATE FUNCTION "public"."uuid_generate_v1"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v1'
  LANGUAGE c VOLATILE STRICT
  COST 1;

-- ----------------------------
-- Function structure for uuid_generate_v1mc
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v1mc"();
CREATE FUNCTION "public"."uuid_generate_v1mc"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v1mc'
  LANGUAGE c VOLATILE STRICT
  COST 1;

-- ----------------------------
-- Function structure for uuid_generate_v3
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v3"("namespace" uuid, "name" text);
CREATE FUNCTION "public"."uuid_generate_v3"("namespace" uuid, "name" text)
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v3'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for uuid_generate_v4
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v4"();
CREATE FUNCTION "public"."uuid_generate_v4"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v4'
  LANGUAGE c VOLATILE STRICT
  COST 1;

-- ----------------------------
-- Function structure for uuid_generate_v5
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v5"("namespace" uuid, "name" text);
CREATE FUNCTION "public"."uuid_generate_v5"("namespace" uuid, "name" text)
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v5'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for uuid_nil
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_nil"();
CREATE FUNCTION "public"."uuid_nil"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_nil'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for uuid_ns_dns
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_ns_dns"();
CREATE FUNCTION "public"."uuid_ns_dns"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_ns_dns'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for uuid_ns_oid
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_ns_oid"();
CREATE FUNCTION "public"."uuid_ns_oid"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_ns_oid'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for uuid_ns_url
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_ns_url"();
CREATE FUNCTION "public"."uuid_ns_url"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_ns_url'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for uuid_ns_x500
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_ns_x500"();
CREATE FUNCTION "public"."uuid_ns_x500"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_ns_x500'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Primary Key structure for table blood_transfusion
-- ----------------------------
ALTER TABLE "public"."blood_transfusion" ADD CONSTRAINT "pk_blood_transfusion" PRIMARY KEY ("link_id", "record_id", "admission_time", "blood_type");

-- ----------------------------
-- Primary Key structure for table electronic_medical_record
-- ----------------------------
ALTER TABLE "public"."electronic_medical_record" ADD CONSTRAINT "pk_electronic_medical_record" PRIMARY KEY ("link_id", "record_id", "admission_time");

-- ----------------------------
-- Primary Key structure for table icu_record
-- ----------------------------
ALTER TABLE "public"."icu_record" ADD CONSTRAINT "pk_icu_record" PRIMARY KEY ("link_id", "record_id", "admission_time", "icu_seq");

-- ----------------------------
-- Primary Key structure for table medical_cost_detail
-- ----------------------------
ALTER TABLE "public"."medical_cost_detail" ADD CONSTRAINT "pk_medical_cost_detail" PRIMARY KEY ("link_id", "record_id", "admission_time", "cost_type");

-- ----------------------------
-- Indexes structure for table medical_order
-- ----------------------------
CREATE INDEX "idx_medical_order_order_name" ON "public"."medical_order" USING btree (
  "order_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_medical_order_patient_name" ON "public"."medical_order" USING btree (
  "patient_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_medical_order_record_id" ON "public"."medical_order" USING btree (
  "record_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_medical_order_record_id_start_time" ON "public"."medical_order" USING btree (
  "record_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "start_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_medical_order_start_time" ON "public"."medical_order" USING btree (
  "start_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_medical_order_unique_id" ON "public"."medical_order" USING btree (
  "unique_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table medical_record
-- ----------------------------
ALTER TABLE "public"."medical_record" ADD CONSTRAINT "pk_medical_record" PRIMARY KEY ("link_id", "record_id", "admission_time");

-- ----------------------------
-- Primary Key structure for table medical_record_front_page
-- ----------------------------
ALTER TABLE "public"."medical_record_front_page" ADD CONSTRAINT "pk_medical_record_front_page" PRIMARY KEY ("link_id", "record_id", "admission_time");

-- ----------------------------
-- Primary Key structure for table nursing_level
-- ----------------------------
ALTER TABLE "public"."nursing_level" ADD CONSTRAINT "pk_nursing_level" PRIMARY KEY ("link_id", "record_id", "admission_time", "nursing_level_type");

-- ----------------------------
-- Primary Key structure for table patient_base
-- ----------------------------
ALTER TABLE "public"."patient_base" ADD CONSTRAINT "pk_patient_base" PRIMARY KEY ("link_id");

-- ----------------------------
-- Primary Key structure for table patient_basic_info
-- ----------------------------
ALTER TABLE "public"."patient_basic_info" ADD CONSTRAINT "pk_patient_basic_info" PRIMARY KEY ("unique_id");

-- ----------------------------
-- Uniques structure for table patient_blood_gas_results
-- ----------------------------
ALTER TABLE "public"."patient_blood_gas_results" ADD CONSTRAINT "patient_blood_gas_results2_patient_serial_number_blood_gas__key" UNIQUE ("patient_serial_number", "blood_gas_id", "test_time");

-- ----------------------------
-- Primary Key structure for table patient_diagnosis
-- ----------------------------
ALTER TABLE "public"."patient_diagnosis" ADD CONSTRAINT "pk_patient_diagnosis" PRIMARY KEY ("link_id", "record_id", "admission_time", "diagnosis_type", "diagnosis_seq");

-- ----------------------------
-- Primary Key structure for table patient_progress_record
-- ----------------------------
ALTER TABLE "public"."patient_progress_record" ADD CONSTRAINT "pk_patient_progress_record" PRIMARY KEY ("link_id", "record_id", "admission_time", "discharge_time", "record_date", "record_name");

-- ----------------------------
-- Indexes structure for table respiratory_support
-- ----------------------------
CREATE INDEX "idx_respiratory_support_unique_id" ON "public"."respiratory_support" USING btree (
  "unique_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table surgical_operation
-- ----------------------------
ALTER TABLE "public"."surgical_operation" ADD CONSTRAINT "pk_surgical_operation" PRIMARY KEY ("link_id", "record_id", "admission_time", "operation_type", "operation_seq");

-- ----------------------------
-- Foreign Keys structure for table blood_transfusion
-- ----------------------------
ALTER TABLE "public"."blood_transfusion" ADD CONSTRAINT "fk_blood_transfusion_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table cost_info
-- ----------------------------
ALTER TABLE "public"."cost_info" ADD CONSTRAINT "fk_cost_info_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table diagnosis_info
-- ----------------------------
ALTER TABLE "public"."diagnosis_info" ADD CONSTRAINT "fk_diagnosis_info_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table electronic_medical_record
-- ----------------------------
ALTER TABLE "public"."electronic_medical_record" ADD CONSTRAINT "fk_electronic_medical_record_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table hospitalization_record
-- ----------------------------
ALTER TABLE "public"."hospitalization_record" ADD CONSTRAINT "fk_hospitalization_record_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table icu_record
-- ----------------------------
ALTER TABLE "public"."icu_record" ADD CONSTRAINT "fk_icu_record_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table imaging_examination_record
-- ----------------------------
ALTER TABLE "public"."imaging_examination_record" ADD CONSTRAINT "fk_imaging_examination_record_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table laboratory_test_record
-- ----------------------------
ALTER TABLE "public"."laboratory_test_record" ADD CONSTRAINT "fk_laboratory_test_record_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table medical_cost_detail
-- ----------------------------
ALTER TABLE "public"."medical_cost_detail" ADD CONSTRAINT "fk_medical_cost_detail_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table medical_order
-- ----------------------------
ALTER TABLE "public"."medical_order" ADD CONSTRAINT "fk_medical_order_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table medical_record
-- ----------------------------
ALTER TABLE "public"."medical_record" ADD CONSTRAINT "fk_medical_record_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table medical_record_front_page
-- ----------------------------
ALTER TABLE "public"."medical_record_front_page" ADD CONSTRAINT "fk_medical_record_front_page_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table nursing_level
-- ----------------------------
ALTER TABLE "public"."nursing_level" ADD CONSTRAINT "fk_nursing_level_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table nursing_record
-- ----------------------------
ALTER TABLE "public"."nursing_record" ADD CONSTRAINT "fk_nursing_record_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table operation_info
-- ----------------------------
ALTER TABLE "public"."operation_info" ADD CONSTRAINT "fk_operation_info_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table patient_basic_info
-- ----------------------------
ALTER TABLE "public"."patient_basic_info" ADD CONSTRAINT "fk_patient_basic_info_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table patient_diagnosis
-- ----------------------------
ALTER TABLE "public"."patient_diagnosis" ADD CONSTRAINT "fk_patient_diagnosis_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table patient_progress_record
-- ----------------------------
ALTER TABLE "public"."patient_progress_record" ADD CONSTRAINT "fk_patient_progress_record_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table surgical_operation
-- ----------------------------
ALTER TABLE "public"."surgical_operation" ADD CONSTRAINT "fk_surgical_operation_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table surgical_progress_record
-- ----------------------------
ALTER TABLE "public"."surgical_progress_record" ADD CONSTRAINT "fk_surgical_progress_record_link_id" FOREIGN KEY ("link_id") REFERENCES "public"."patient_base" ("link_id") ON DELETE NO ACTION ON UPDATE NO ACTION;
