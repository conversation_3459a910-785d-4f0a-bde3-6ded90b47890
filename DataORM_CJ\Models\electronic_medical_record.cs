﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 患者电子病历表
/// </summary>
public partial class electronic_medical_record
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    /// <summary>
    /// 住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string patient_name { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int age { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string gender { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateOnly birth_date { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    public DateOnly admission_time { get; set; }

    /// <summary>
    /// 出院时间
    /// </summary>
    public DateOnly discharge_time { get; set; }

    /// <summary>
    /// 入院诊断
    /// </summary>
    public string admission_diagnosis { get; set; }

    /// <summary>
    /// 现病史
    /// </summary>
    public string present_illness { get; set; }

    /// <summary>
    /// 既往史
    /// </summary>
    public string past_history { get; set; }

    /// <summary>
    /// 个人史
    /// </summary>
    public string personal_history { get; set; }

    /// <summary>
    /// 过敏史
    /// </summary>
    public string allergy_history { get; set; }

    /// <summary>
    /// 家族史
    /// </summary>
    public string family_history { get; set; }

    /// <summary>
    /// 月经史
    /// </summary>
    public string menstrual_history { get; set; }

    /// <summary>
    /// 婚育史
    /// </summary>
    public string marriage_history { get; set; }

    /// <summary>
    /// 主诉
    /// </summary>
    public string chief_complaint { get; set; }

    /// <summary>
    /// 体格检查
    /// </summary>
    public string physical_examination { get; set; }

    /// <summary>
    /// 出院诊断
    /// </summary>
    public string discharge_diagnosis { get; set; }

    /// <summary>
    /// 出院记录
    /// </summary>
    public string discharge_record { get; set; }

    public virtual patient_base link { get; set; }
}