namespace ControlCenter
{
    partial class EtlRulesControl
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;



        #region 组件设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.toolStripEtlRules = new System.Windows.Forms.ToolStrip();
            this.toolStripLabel1 = new System.Windows.Forms.ToolStripLabel();
            this.toolStripStatusLabel = new System.Windows.Forms.ToolStripLabel();
            this.dataGridViewEtlRules = new System.Windows.Forms.DataGridView();
            this.toolStripEtlRules.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewEtlRules)).BeginInit();
            this.SuspendLayout();
            //
            // toolStripEtlRules
            //
            this.toolStripEtlRules.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripLabel1,
            this.toolStripStatusLabel});
            this.toolStripEtlRules.Location = new System.Drawing.Point(0, 0);
            this.toolStripEtlRules.Name = "toolStripEtlRules";
            this.toolStripEtlRules.Size = new System.Drawing.Size(400, 25);
            this.toolStripEtlRules.TabIndex = 0;
            this.toolStripEtlRules.Text = "toolStrip1";
            //
            // toolStripLabel1
            //
            this.toolStripLabel1.Name = "toolStripLabel1";
            this.toolStripLabel1.Size = new System.Drawing.Size(92, 22);
            this.toolStripLabel1.Text = "ETL数据规则";
            //
            // toolStripStatusLabel
            //
            this.toolStripStatusLabel.Name = "toolStripStatusLabel";
            this.toolStripStatusLabel.Size = new System.Drawing.Size(32, 22);
            this.toolStripStatusLabel.Text = "就绪";
            this.toolStripStatusLabel.Alignment = System.Windows.Forms.ToolStripItemAlignment.Right;
            //
            // dataGridViewEtlRules
            //
            this.dataGridViewEtlRules.AllowUserToAddRows = false;
            this.dataGridViewEtlRules.AllowUserToDeleteRows = false;
            this.dataGridViewEtlRules.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewEtlRules.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewEtlRules.Location = new System.Drawing.Point(0, 25);
            this.dataGridViewEtlRules.Name = "dataGridViewEtlRules";
            this.dataGridViewEtlRules.ReadOnly = true;
            this.dataGridViewEtlRules.RowTemplate.Height = 25;
            this.dataGridViewEtlRules.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewEtlRules.Size = new System.Drawing.Size(400, 275);
            this.dataGridViewEtlRules.TabIndex = 1;
            this.dataGridViewEtlRules.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            //
            // EtlRulesControl
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.dataGridViewEtlRules);
            this.Controls.Add(this.toolStripEtlRules);
            this.Name = "EtlRulesControl";
            this.Size = new System.Drawing.Size(400, 300);
            this.toolStripEtlRules.ResumeLayout(false);
            this.toolStripEtlRules.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewEtlRules)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private System.Windows.Forms.ToolStrip toolStripEtlRules;
        private System.Windows.Forms.ToolStripLabel toolStripLabel1;
        private System.Windows.Forms.ToolStripLabel toolStripStatusLabel;
        private System.Windows.Forms.DataGridView dataGridViewEtlRules;
    }
}
