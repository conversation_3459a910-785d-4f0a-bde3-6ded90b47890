using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using DataORM_CJ.Models;
using System.ComponentModel;
using System.Reflection;

namespace ControlCenter
{
    public partial class PatientListControl : UserControl
    {
        private chinaContext? _context;
        private SortableBindingList<patient_basic_info>? _patients;
        private Dictionary<string, bool> _columnVisibility = new Dictionary<string, bool>();

        /// <summary>
        /// 患者选择事件
        /// </summary>
        public event EventHandler<patient_basic_info>? PatientSelected;

        /// <summary>
        /// 多个患者选择事件
        /// </summary>
        public event EventHandler<List<patient_basic_info>>? PatientsSelected;

        /// <summary>
        /// 状态变化事件
        /// </summary>
        public event EventHandler<string>? StatusChanged;

        public PatientListControl()
        {
            InitializeComponent();
            if (!DesignMode)
            {
                InitializeColumnSelection();
            }
        }

        /// <summary>
        /// 初始化数据库连接并加载数据
        /// </summary>
        /// <param name="connectionString">数据库连接字符串，如果为空则从配置文件读取</param>
        public async Task InitializeAsync(string? connectionString = null)
        {
            try
            {
                InitializeDatabase(connectionString);
                await LoadPatientDataAsync();
            }
            catch (Exception ex)
            {
                OnStatusChanged($"初始化失败：{ex.Message}");
                MessageBox.Show($"初始化患者列表失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 刷新患者数据
        /// </summary>
        public async Task RefreshAsync()
        {
            if (_context != null)
            {
                await LoadPatientDataAsync();
            }
        }

        /// <summary>
        /// 获取当前选中的患者
        /// </summary>
        public patient_basic_info? GetSelectedPatient()
        {
            if (dataGridViewPatients.CurrentRow?.DataBoundItem is patient_basic_info patient)
            {
                return patient;
            }
            return null;
        }

        /// <summary>
        /// 获取所有选中的患者
        /// </summary>
        public List<patient_basic_info> GetSelectedPatients()
        {
            var selectedPatients = new List<patient_basic_info>();
            foreach (DataGridViewRow row in dataGridViewPatients.SelectedRows)
            {
                if (row.DataBoundItem is patient_basic_info patient)
                {
                    selectedPatients.Add(patient);
                }
            }
            return selectedPatients;
        }

        /// <summary>
        /// 获取DataGridView控件（用于访问列信息）
        /// </summary>
        public DataGridView GetDataGridView()
        {
            return dataGridViewPatients;
        }

        private void InitializeDatabase(string? connectionString = null)
        {
            if (string.IsNullOrEmpty(connectionString))
            {
                // 读取配置文件
                var configuration = new ConfigurationBuilder()
                    .AddJsonFile("appsettings.json", optional: true)
                    .Build();

                connectionString = configuration.GetConnectionString("DefaultConnection");
            }

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("未找到数据库连接字符串");
            }

            var optionsBuilder = new DbContextOptionsBuilder<chinaContext>();
            optionsBuilder.UseNpgsql(connectionString);
            _context = new chinaContext(optionsBuilder.Options);
        }

        private async Task LoadPatientDataAsync()
        {
            if (_context == null) return;

            try
            {
                // 显示加载提示
                dataGridViewPatients.DataSource = null;
                OnStatusChanged("正在加载患者数据...");

                // 异步加载数据
                var patientData = await _context.patient_basic_info.ToListAsync();
                _patients = new SortableBindingList<patient_basic_info>(patientData);

                // 绑定数据
                dataGridViewPatients.DataSource = _patients;

                // 应用列显示设置
                ApplyColumnVisibility();

                OnStatusChanged($"共加载 {_patients.Count} 条患者记录");
            }
            catch (Exception ex)
            {
                OnStatusChanged("数据加载失败");
                MessageBox.Show($"加载患者数据失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeColumnSelection()
        {
            // 获取patient_basic_info的所有属性
            var properties = typeof(patient_basic_info).GetProperties()
                .Where(p => p.Name != "link") // 排除导航属性
                .ToList();

            // 创建列选择菜单项
            foreach (var prop in properties)
            {
                // 获取中文显示名称
                var displayName = GetDisplayName(prop);
                var isVisible = IsDefaultVisible(prop.Name);

                _columnVisibility[prop.Name] = isVisible;

                // 创建菜单项
                var menuItem = new ToolStripMenuItem(displayName)
                {
                    Checked = isVisible,
                    CheckOnClick = true,
                    Tag = prop.Name
                };
                menuItem.Click += ColumnMenuItem_Click;

                toolStripDropDownColumns.DropDownItems.Add(menuItem);
            }
        }

        private string GetDisplayName(PropertyInfo property)
        {
            // 从中文注释映射获取显示名称
            var chineseNames = new Dictionary<string, string>
            {
                {"unique_id", "唯一号"},
                {"link_id", "关联号"},
                {"record_id", "病案号"},
                {"health_card_number", "健康卡号"},
                {"name", "姓名"},
                {"gender", "性别"},
                {"age", "年龄"},
                {"birth_date", "出生日期"},
                {"nationality", "国籍"},
                {"marital_status", "婚姻状况"},
                {"occupation", "职业"},
                {"ethnicity", "民族"},
                {"id_type", "证件类别"},
                {"id_number", "身份证号"},
                {"birth_address", "出生地址"},
                {"native_province", "籍贯省"},
                {"household_address", "户口地址"},
                {"household_postal_code", "户口邮编"},
                {"current_address", "现住址"},
                {"current_address_phone", "现住址电话"},
                {"current_address_postal_code", "现住址邮编"},
                {"work_unit_address", "工作单位地址"},
                {"work_unit_phone", "工作单位电话"},
                {"work_unit_postal_code", "工作单位邮编"},
                {"contact_name", "联系人姓名"},
                {"contact_relationship", "联系人关系"},
                {"contact_address", "联系人地址"},
                {"contact_phone", "联系人电话"},
                {"has_drug_allergy", "有无药物过敏"},
                {"allergic_drug_name", "过敏药物名称"},
                {"hbsag", "HBsAg"},
                {"hcv_ab", "HCV-Ab"},
                {"hiv_ab", "HIV-Ab"},
                {"abo_blood_type", "ABO血型"},
                {"rh_blood_type", "Rh血型"}
            };

            return chineseNames.TryGetValue(property.Name, out var displayName) ? displayName : property.Name;
        }

        private bool IsDefaultVisible(string propertyName)
        {
            // 默认显示的列
            var defaultVisibleColumns = new[]
            {
                "link_id", "record_id", "name", "gender", "age", "birth_date"
            };

            return defaultVisibleColumns.Contains(propertyName);
        }

        private void ColumnMenuItem_Click(object? sender, EventArgs e)
        {
            if (sender is ToolStripMenuItem menuItem && menuItem.Tag is string propertyName)
            {
                _columnVisibility[propertyName] = menuItem.Checked;
                ApplyColumnVisibility();
            }
        }

        private void ApplyColumnVisibility()
        {
            if (dataGridViewPatients.DataSource == null) return;

            foreach (DataGridViewColumn column in dataGridViewPatients.Columns)
            {
                if (_columnVisibility.TryGetValue(column.DataPropertyName, out var isVisible))
                {
                    column.Visible = isVisible;
                    column.SortMode = DataGridViewColumnSortMode.Automatic;

                    // 设置中文表头
                    var property = typeof(patient_basic_info).GetProperty(column.DataPropertyName);
                    if (property != null)
                    {
                        column.HeaderText = GetDisplayName(property);
                    }
                }
            }

            // 自动调整列宽
            dataGridViewPatients.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.DisplayedCells);
        }

        private void OnStatusChanged(string status)
        {
            toolStripStatusLabel.Text = status;
            StatusChanged?.Invoke(this, status);
        }

        private void OnPatientSelected(patient_basic_info patient)
        {
            PatientSelected?.Invoke(this, patient);
        }

        private void OnPatientsSelected(List<patient_basic_info> patients)
        {
            PatientsSelected?.Invoke(this, patients);
        }

        private void DataGridViewPatients_SelectionChanged(object? sender, EventArgs e)
        {
            // 触发单选事件（保持向后兼容）
            var selectedPatient = GetSelectedPatient();
            if (selectedPatient != null)
            {
                OnPatientSelected(selectedPatient);
            }

            // 触发多选事件
            var selectedPatients = GetSelectedPatients();
            OnPatientsSelected(selectedPatients);
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            if (!DesignMode)
            {
                // 绑定选择事件
                dataGridViewPatients.SelectionChanged += DataGridViewPatients_SelectionChanged;
            }
        }
    }

    // Helper class for comparing properties
    internal class PropertyComparer<T> : IComparer<T>
    {
        private readonly PropertyDescriptor _propertyDescriptor;
        private readonly ListSortDirection _sortDirection;

        public PropertyComparer(PropertyDescriptor propertyDescriptor, ListSortDirection sortDirection)
        {
            _propertyDescriptor = propertyDescriptor ?? throw new ArgumentNullException(nameof(propertyDescriptor));
            _sortDirection = sortDirection;
        }

        public int Compare(T? x, T? y)
        {
            if (x == null && y == null) return 0;
            if (x == null) return _sortDirection == ListSortDirection.Ascending ? -1 : 1;
            if (y == null) return _sortDirection == ListSortDirection.Ascending ? 1 : -1;

            object? xValue = _propertyDescriptor.GetValue(x);
            object? yValue = _propertyDescriptor.GetValue(y);

            int result;
            if (xValue == null && yValue == null)
            {
                result = 0;
            }
            else if (xValue == null)
            {
                result = -1;
            }
            else if (yValue == null)
            {
                result = 1;
            }
            else if (xValue is IComparable xComparable)
            {
                result = xComparable.CompareTo(yValue);
            }
            else if (xValue.Equals(yValue))
            {
                result = 0;
            }
            else
            {
                result = xValue.ToString()?.CompareTo(yValue.ToString()) ?? 0;
            }

            return _sortDirection == ListSortDirection.Ascending ? result : -result;
        }
    }

    // Sortable BindingList
    public class SortableBindingList<T> : BindingList<T>
    {
        private bool _isSorted;
        private ListSortDirection _sortDirection = ListSortDirection.Ascending;
        private PropertyDescriptor? _sortProperty;

        public SortableBindingList() : base() { }

        public SortableBindingList(IList<T> list) : base(list) { }

        protected override bool SupportsSortingCore => true;

        protected override bool IsSortedCore => _isSorted;

        protected override ListSortDirection SortDirectionCore => _sortDirection;

        protected override PropertyDescriptor? SortPropertyCore => _sortProperty;

        protected override void ApplySortCore(PropertyDescriptor prop, ListSortDirection direction)
        {
            _sortProperty = prop;
            _sortDirection = direction;

            if (Items is List<T> list)
            {
                list.Sort(new PropertyComparer<T>(prop, direction));
                _isSorted = true;
            }
            else
            {
                _isSorted = false; // Or handle other IList<T> implementations if necessary
            }

            OnListChanged(new ListChangedEventArgs(ListChangedType.Reset, -1));
        }

        protected override void RemoveSortCore()
        {
            _isSorted = false;
            _sortProperty = null;
            // Optionally, you might want to revert to the original order if you stored it.
            // For simplicity, this implementation doesn't restore original order.
            OnListChanged(new ListChangedEventArgs(ListChangedType.Reset, -1));
        }
    }
} 