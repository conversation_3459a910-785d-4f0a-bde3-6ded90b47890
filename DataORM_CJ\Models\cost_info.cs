﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataORM_CJ.Models;

/// <summary>
/// 费用信息表
/// </summary>
public partial class cost_info
{
    /// <summary>
    /// 关联号
    /// </summary>
    public string link_id { get; set; }

    /// <summary>
    /// 唯一号
    /// </summary>
    public string unique_id { get; set; }

    /// <summary>
    /// 病案号/住院号/门诊号
    /// </summary>
    public string record_id { get; set; }

    /// <summary>
    /// 住院总费用
    /// </summary>
    public string total_cost { get; set; }

    /// <summary>
    /// 住院总费用其中自付金额
    /// </summary>
    public string self_payment_amount { get; set; }

    /// <summary>
    /// 1.一般医疗服务费
    /// </summary>
    public string general_medical_service_fee { get; set; }

    /// <summary>
    /// 2.一般治疗操作费
    /// </summary>
    public string general_treatment_operation_fee { get; set; }

    /// <summary>
    /// 3.护理费
    /// </summary>
    public string nursing_fee { get; set; }

    /// <summary>
    /// 4.综合医疗服务类其他费用
    /// </summary>
    public string other_comprehensive_medical_service_fee { get; set; }

    /// <summary>
    /// 5.病理诊断费
    /// </summary>
    public string pathological_diagnosis_fee { get; set; }

    /// <summary>
    /// 6.实验室诊断费
    /// </summary>
    public string laboratory_diagnosis_fee { get; set; }

    /// <summary>
    /// 7.影像学诊断费
    /// </summary>
    public string imaging_diagnosis_fee { get; set; }

    /// <summary>
    /// 8.临床诊断项目费
    /// </summary>
    public string clinical_diagnosis_project_fee { get; set; }

    /// <summary>
    /// 9.非手术治疗项目费
    /// </summary>
    public string non_surgical_treatment_project_fee { get; set; }

    /// <summary>
    /// 其中：临床物理治疗费
    /// </summary>
    public string clinical_physical_therapy_fee { get; set; }

    /// <summary>
    /// 10.手术治疗费
    /// </summary>
    public string surgical_treatment_fee { get; set; }

    /// <summary>
    /// 其中：麻醉费
    /// </summary>
    public string anesthesia_fee { get; set; }

    /// <summary>
    /// 其中：手术费
    /// </summary>
    public string surgery_fee { get; set; }

    /// <summary>
    /// 11.康复费
    /// </summary>
    public string rehabilitation_fee { get; set; }

    /// <summary>
    /// 12.中医治疗费
    /// </summary>
    public string traditional_chinese_medicine_treatment_fee { get; set; }

    /// <summary>
    /// 13.西药费
    /// </summary>
    public string western_medicine_fee { get; set; }

    /// <summary>
    /// 其中：抗菌药物费
    /// </summary>
    public string antibacterial_drug_fee { get; set; }

    /// <summary>
    /// 14.中成药费
    /// </summary>
    public string chinese_patent_medicine_fee { get; set; }

    /// <summary>
    /// 15.中草药费
    /// </summary>
    public string chinese_herbal_medicine_fee { get; set; }

    /// <summary>
    /// 16.血费
    /// </summary>
    public string blood_fee { get; set; }

    /// <summary>
    /// 17.白蛋白类制品费
    /// </summary>
    public string albumin_product_fee { get; set; }

    /// <summary>
    /// 18.球蛋白类制品费
    /// </summary>
    public string globulin_product_fee { get; set; }

    /// <summary>
    /// 19.凝血因子类制品费
    /// </summary>
    public string coagulation_factor_product_fee { get; set; }

    /// <summary>
    /// 20.细胞因子类制品费
    /// </summary>
    public string cytokine_product_fee { get; set; }

    /// <summary>
    /// 21.检查用一次性医用材料费
    /// </summary>
    public string examination_disposable_medical_material_fee { get; set; }

    /// <summary>
    /// 22.治疗用一次性医用材料费
    /// </summary>
    public string treatment_disposable_medical_material_fee { get; set; }

    /// <summary>
    /// 23.手术用一次性医用材料费
    /// </summary>
    public string surgical_disposable_medical_material_fee { get; set; }

    /// <summary>
    /// 24.其他费
    /// </summary>
    public string other_fee { get; set; }

    public virtual patient_base link { get; set; }
}