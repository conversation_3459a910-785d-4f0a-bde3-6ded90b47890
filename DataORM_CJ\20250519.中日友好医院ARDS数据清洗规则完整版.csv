﻿任务编号,是否提取,字典名称,提取位置,备注,,
ETL_CJ_101,,基本信息_中心编号,,,,
ETL_CJ_102,,基本信息_患者编号,,,,
ETL_CJ_103,,基本信息_患者姓名,,,,
ETL_CJ_104,,基本信息_病历住院号,,,,
ETL_CJ_105,,基本信息_纳入时间(YYYY-MM-DD),,,,
ETL_CJ_106,本次需提取,ARDS入选标准_氧合指标_严重程度(轻度=1/中度=2/重度=3),,,,
ETL_CJ_107,本次需提取,入组前基线资料_姓名缩写,,,,
ETL_CJ_108,本次需提取,入组前基线资料_病案号,病案首页,,,
ETL_CJ_109,本次需提取,入组前基线资料_性别(男=1/女=2),病案首页,,,
ETL_CJ_110,本次需提取,入组前基线资料_年龄,病案首页,,,
ETL_CJ_111,本次需提取,入组前基线资料_身高,护理记录,,,
ETL_CJ_112,本次需提取,入组前基线资料_体重,护理记录,,,
ETL_CJ_113,本次需提取,入组前基线资料_入本院时间,病案首页,,,
ETL_CJ_114,本次需提取,入组前基线资料_入本ICU时间,病案首页,重症监护室进入时间1  【ACX列】,,
ETL_CJ_115,本次需提取,入组前基线资料_联系方式,病案首页,,,
ETL_CJ_116,本次需提取,入院诊断,电子病历,每一个诊断之间用分隔符号分开，分隔符号需要可以用作数据分析,,
ETL_CJ_117,本次需提取,出院诊断,病案首页,每一个诊断之间用分隔符号分开，分隔符号需要可以用作数据分析,,
ETL_CJ_118,本次需提取,内科基础疾病史_合并内科基础疾病(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_119,本次需提取,内科基础疾病史_心血管疾病_高血压(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_120,本次需提取,内科基础疾病史_心血管疾病_冠心病(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_121,本次需提取,内科基础疾病史_心血管疾病_瓣膜病(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_122,本次需提取,内科基础疾病史_心血管疾病_心肌病(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_123,本次需提取,内科基础疾病史_心血管疾病_慢性心功能不全(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_124,本次需提取,内科基础疾病史_心血管疾病_心房颤动(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_125,本次需提取,内科基础疾病史_心血管疾病_高脂血症(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_126,本次需提取,内科基础疾病史_心血管疾病_其他,【电子病历】--{【既往史】+【现病史】},填写除上面以外的【心血管疾病】名称/诊断，填写文本,,
ETL_CJ_127,本次需提取,内科基础疾病史_呼吸系统疾病_慢性阻塞性肺疾病(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_128,本次需提取,内科基础疾病史_呼吸系统疾病_肺结核(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_129,本次需提取,内科基础疾病史_呼吸系统疾病_支气管哮喘(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_130,本次需提取,内科基础疾病史_呼吸系统疾病_间质性肺疾病(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_131,本次需提取,内科基础疾病史_呼吸系统疾病_慢性肺源性心脏病(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_132,本次需提取,内科基础疾病史_呼吸系统疾病_支气管扩张症(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_133,本次需提取,内科基础疾病史_呼吸系统疾病_其他,【电子病历】--{【既往史】+【现病史】},填写除上面以外的【呼吸系统疾病】名称/诊断，填写文本,,
ETL_CJ_134,本次需提取,内科基础疾病史_消化系统疾病_消化性溃疡(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_135,本次需提取,内科基础疾病史_消化系统疾病_克罗恩病(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_136,本次需提取,内科基础疾病史_消化系统疾病_溃疡性结肠炎(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_137,本次需提取,内科基础疾病史_消化系统疾病_慢性肝炎(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_138,本次需提取,内科基础疾病史_消化系统疾病_肝硬化(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_139,本次需提取,内科基础疾病史_消化系统疾病_其他,【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_140,本次需提取,内科基础疾病史_泌尿系统疾病_慢性肾功能不全(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_141,本次需提取,内科基础疾病史_泌尿系统疾病_肾病综合征(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_142,本次需提取,内科基础疾病史_泌尿系统疾病_其他,【电子病历】--{【既往史】+【现病史】},填写除上面以外的【泌尿系统疾病】名称/诊断，填写文本,,
ETL_CJ_143,本次需提取,内科基础疾病史_神经系统疾病_缺血性脑卒中(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_144,本次需提取,内科基础疾病史_神经系统疾病_出血性脑卒中(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_145,本次需提取,内科基础疾病史_神经系统疾病_其他,【电子病历】--{【既往史】+【现病史】},填写除上面以外的【神经系统疾病】名称/诊断，填写文本,,
ETL_CJ_146,本次需提取,内科基础疾病史_代谢及内分泌系统疾病_糖尿病(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_147,本次需提取,内科基础疾病史_代谢及内分泌系统疾病_甲状腺功能亢进(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_148,本次需提取,内科基础疾病史_代谢及内分泌系统疾病_甲状腺功能减低(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_149,本次需提取,内科基础疾病史_代谢及内分泌系统疾病_其他,【电子病历】--{【既往史】+【现病史】},填写除上面以外的【代谢及内分泌系统疾病】名称/诊断，填写文本,,
ETL_CJ_150,本次需提取,内科基础疾病史_风湿免疫系统疾病_系统性红斑狼疮(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_151,本次需提取,内科基础疾病史_风湿免疫系统疾病_肌炎皮肌炎(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_152,本次需提取,内科基础疾病史_风湿免疫系统疾病_系统性血管炎(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_153,本次需提取,内科基础疾病史_风湿免疫系统疾病_类风湿性关节炎(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_154,本次需提取,内科基础疾病史_风湿免疫系统疾病_干燥综合征(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_155,本次需提取,内科基础疾病史_风湿免疫系统疾病_抗磷脂综合征(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_156,本次需提取,内科基础疾病史_风湿免疫系统疾病_贝赫切特综合征(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_157,本次需提取,内科基础疾病史_风湿免疫系统疾病_系统性肝硬化症(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_158,,内科基础疾病史_风湿免疫系统疾病__其他,【电子病历】--{【既往史】+【现病史】},填写除上面以外的【血管性疾病】名称/诊断，填写文本,,
ETL_CJ_159,本次需提取,内科基础疾病史_血液系统疾病_白血病(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_160,本次需提取,内科基础疾病史_血液系统疾病_真性红细胞增多症(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_161,本次需提取,内科基础疾病史_血液系统疾病_骨髓瘤(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_162,本次需提取,内科基础疾病史_血液系统疾病_淋巴瘤(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_163,本次需提取,内科基础疾病史_血液系统疾病_原发性血小板增多症(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_164,本次需提取,内科基础疾病史_血液系统疾病_骨髓增生异常综合征(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_165,本次需提取,内科基础疾病史_血液系统疾病_阵发性睡眠性血红蛋白尿(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_166,本次需提取,内科基础疾病史_血液系统疾病_其他,【电子病历】--{【既往史】+【现病史】},填写除上面以外的【血液系统疾病】名称/诊断，填写文本,,
ETL_CJ_167,本次需提取,内科基础疾病史_血管性疾病_静脉炎(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_168,本次需提取,内科基础疾病史_血管性疾病_静脉曲张(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_169,本次需提取,内科基础疾病史_血管性疾病_其他,【电子病历】--{【既往史】+【现病史】},填写除上面以外的【血管性疾病】名称/诊断，填写文本,,
ETL_CJ_170,本次需提取,内科基础疾病史_其他基础内科疾病(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_171,,内科基础疾病史_其他基础内科疾病_其他,【电子病历】--{【既往史】+【现病史】},填写除上面以外的【基础内科疾病】名称/诊断，填写文本,,
ETL_CJ_172,本次需提取,免疫抑制人群_免疫抑制人群(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_173,本次需提取,免疫抑制人群_先天性免疫缺陷(无=0/有=1),【电子病历】--{【既往史】+【现病史】},如果以下四题选择“无”本题，则选择“无”，②如果以下四题任意一题选择“有”，则本题选择“有”,,
ETL_CJ_174,本次需提取,免疫抑制人群_获得性免疫缺陷HIV(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_175,本次需提取,免疫抑制人群_实体器官移植受体(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_176,本次需提取,免疫抑制人群_骨髓移植受体(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_177,本次需提取,免疫抑制人群_长期激素治疗(无=0/有=1),【电子病历】--{【既往史】+【现病史】},如果患者应用了“泼尼松、强的松（即泼尼松）、甲基强的松龙（甲强龙）、地塞米松、氢化可的松，中的任意一种，则本题选择“有”，没有应用，则选择“无”,,
ETL_CJ_178,本次需提取,免疫抑制人群_过去三月内接受生物免疫调节剂治疗(无=0/有=1),【电子病历】--{【既往史】+【现病史】},如果以下七道题选择“无”本题，则选择“无”，②如果以下七道题任意一题选择“有”，则本题选择“有”,,
ETL_CJ_179,本次需提取,免疫抑制人群_T细胞清除（抗胸腺细胞球蛋白、阿仑单抗）(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_180,本次需提取,免疫抑制人群_B细胞清除(利妥昔单抗)(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_181,本次需提取,免疫抑制人群_钙调磷酸酶抑制剂（环孢素和他克莫司）(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_182,本次需提取,免疫抑制人群_抗代谢产物（霉酚酸酯）(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_183,本次需提取,免疫抑制人群_雷帕霉素抑制剂（西罗莫司、依维莫司）(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_184,本次需提取,免疫抑制人群_肿瘤坏死因子抑制剂（阿达木单抗）(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_185,本次需提取,免疫抑制人群_JAK抑制剂（如托法替布、巴瑞替尼）(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_186,本次需提取,免疫抑制人群_过去三月内接受抗风湿药物或其他免疫抑制剂(无=0/有=1),【电子病历】--{【既往史】+【现病史】},如果以下三道题选择“无”本题，则选择“无”，②如果以下三道题任意一题选择“有”，则本题选择“有”,,
ETL_CJ_187,本次需提取,免疫抑制人群_环磷酰胺(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_188,本次需提取,免疫抑制人群_羟氯喹(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_189,本次需提取,免疫抑制人群_甲氨蝶呤(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_190,本次需提取,免疫抑制人群_活动性或1年以内的恶性肿瘤(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_191,本次需提取,免疫抑制人群_过去三月内接受化疗(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_192,本次需提取,是否合并恶性肿瘤(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_193,本次需提取,恶性肿瘤类型_活动期肿瘤(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_194,本次需提取,恶性肿瘤类型_消化系统(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_195,本次需提取,恶性肿瘤类型_呼吸系统(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_196,本次需提取,恶性肿瘤类型_淋巴造血系统(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_197,本次需提取,恶性肿瘤类型_泌尿和男性生殖系统(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_198,本次需提取,恶性肿瘤类型_女性生殖系统(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_199,本次需提取,恶性肿瘤类型_中枢神经系统(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_200,本次需提取,恶性肿瘤类型_乳腺(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_201,本次需提取,恶性肿瘤类型_头颈(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_202,本次需提取,恶性肿瘤类型_内分泌(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_203,本次需提取,恶性肿瘤类型_皮肤(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_204,本次需提取,恶性肿瘤类型_骨及软组织(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_205,本次需提取,恶性肿瘤类型_其他,【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_206,本次需提取,恶性肿瘤类型_非活动期肿瘤(无=0/有=1),【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_207,本次需提取,恶性肿瘤类型_诊断时间,【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_208,本次需提取,入组前针对SCAP使用激素情况(无=0/有=1),【电子病历】--{【既往史】+【现病史】},只填写针对【SCAP】使用的激素（泼尼松、强的松、甲基强的松龙、甲强松、地塞米松、氢化可的松），治疗其他疾病的激素不包含在本题中,,
ETL_CJ_209,本次需提取,入组前针对SCAP使用激素情况_诊断时间,【电子病历】--{【既往史】+【现病史】},,,
ETL_CJ_210,本次需提取,入组前针对SCAP使用激素情况_激素总剂量,【电子病历】--{【既往史】+【现病史】},激素换算关系：强的松（即泼尼松）5mg=甲基强的松龙（甲强龙）4mg=地塞米松0.75mg=氢化可的松20mg：   ,,
ETL_CJ_211,本次需提取,入组前情况_症状_发热(无=0/有=1),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,,,
ETL_CJ_212,本次需提取,入组前情况_症状_咳嗽(无=0/有=1),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,,,
ETL_CJ_213,本次需提取,入组前情况_症状_咳痰(无=0/有=1),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,,,
ETL_CJ_214,本次需提取,入组前情况_症状_咳痰类型(黄痰=1/白痰=2/血痰=3),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,,,
ETL_CJ_215,本次需提取,入组前情况_症状_胸痛(无=0/有=1),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,,,
ETL_CJ_216,本次需提取,入组前情况_症状_呼吸困难(无=0/有=1),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,,,
ETL_CJ_217,本次需提取,入组前情况_症状_腹泻呕吐(无=0/有=1),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,,,
ETL_CJ_218,本次需提取,入组前情况_症状_头痛(无=0/有=1),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,,,
ETL_CJ_219,本次需提取,入组前情况_症状_心悸(无=0/有=1),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,,,
ETL_CJ_220,本次需提取,入组前情况_症状_少尿(无=0/有=1),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,,,
ETL_CJ_221,本次需提取,入组前情况_症状_意识障碍(无=0/有=1),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,,,
ETL_CJ_222,本次需提取,入组前情况_症状_下肢肿胀(无=0/有=1),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,,,
ETL_CJ_223,本次需提取,入组前情况_影像学特征_单肺单叶段受累(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_224,本次需提取,入组前情况_影像学特征_单肺两个及以上叶段受累(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_225,本次需提取,入组前情况_影像学特征_双肺受累(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_226,本次需提取,入组前情况_影像学特征_磨玻璃影，肺间质改变(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_227,本次需提取,入组前情况_影像学特征_网格影(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_228,本次需提取,入组前情况_影像学特征_实变(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_229,本次需提取,入组前情况_影像学特征_结节(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_230,本次需提取,入组前情况_影像学特征_空洞(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_231,本次需提取,入组前情况_影像学特征_气液平(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_232,本次需提取,入组前情况_影像学特征_胸腔积液(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_233,本次需提取,入组前情况_影像学特征_纵隔淋巴结肿大(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_234,本次需提取,入组前情况_影像学特征_气胸(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_235,本次需提取,入组前情况_影像学特征_纵膈气肿(无=0/有=1),"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_236,本次需提取,入组前情况_检查时间,"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_237,本次需提取,入组前情况_影像所见,"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_238,本次需提取,入组前情况_影像结论,"【影像学检查】条件①[检查时间]在【病案首页】-[重症监护室进入时间1]前后48h内；条件②检查项目：[胸部CT平扫,低剂量胸部CT平扫,胸、全腹、盆腔CT平扫,胸部高分辨CT平扫（HRCT含三维重建）,胸、全腹CT平扫,胸正位片,胸部CT增强,床旁胸正位片,胸正位片+床旁数字化摄影加收、肺小结节低剂量CT成像]对应的[影像所见]列中的信息",,,
ETL_CJ_239,本次需提取,入组时情况_GCS评分_睁眼反应(4=自然睁眼/3=呼唤会睁眼/2=有刺激或痛楚会睁眼/1=对于刺激无反应),①根据【病案首页】-[入院病房]如果内容含“MICU”，则查看【电子病历】--[体格检查]内容计算出得分；②根据【病案首页】-[入院病房]如果内容不含“MICU”，则查看【病程记录】表-[转出记录]-[病程记录]-转入科室：MICU时，查看本条记录的“目前情况”，中的内容进行评分,找不到相关症状，则填写4,一种是入院直接入住 ICU 的患者，GCS根据 电子病历 里体格检查的一般情况来评；另一种是先在普通病房后转入ICU的患者，需要根据病程记录里过滤出“记录名称”列为“转入记录”的病程，根据目前情况来评。转入科室是否为ICU需要看“转出记录”里转出科室是否为MICU。,
ETL_CJ_240,本次需提取,入组时情况_GCS评分_语言反应(5=说话有条理/4=可应答但有答非所问的情形/3=可说出单字/2=可发出声音/1=无任何反应),①根据【病案首页】-[入院病房]如果内容含“MICU”，则查看【电子病历】--[体格检查]内容计算出得分；②根据【病案首页】-[入院病房]如果内容不含“MICU”，则查看【病程记录】表-[转出记录]-[病程记录]-转入科室：MICU时，查看本条记录的“目前情况”，中的内容进行评分,找不到相关症状，则填写5,,
ETL_CJ_241,本次需提取,入组时情况_GCS评分_肢体运动(6=可依指令动作/5=施以刺激时可定位出疼痛位置/4=对疼痛刺激有反应肢体会回缩/3=对疼痛刺激有反应肢体会弯曲/2=对疼痛刺激有反应肢体会伸直/1=无任何反应),①根据【病案首页】-[入院病房]如果内容含“MICU”，则查看【电子病历】--[体格检查]内容计算出得分；②根据【病案首页】-[入院病房]如果内容不含“MICU”，则查看【病程记录】表-[转出记录]-[病程记录]-转入科室：MICU时，查看本条记录的“目前情况”，中的内容进行评分,找不到相关症状，则填写6,,
ETL_CJ_242,本次需提取,入组时情况_GCS评分_总分,以上3道题得分相加,,,
ETL_CJ_243,本次需提取,入组时情况_APACHE II评分_TC体温 ○≥41/≤29.9(+4分) ○39-40.9/30-31.9(+3分) ○32-33.9(+2分) ○38.5-38.9/34-35.9(+1分) ○36-38.5(0分),【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【护理记录】中查找[项目名称]-体温，对应的[项目值(数值)]以此内容得出本题对应的选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,,,
ETL_CJ_244,本次需提取,入组时情况_APACHE II评分_MAP ○≥160/≤49(+4分) ○130-159(+3分) ○110-129/50-69(+2分) ○70-109(0分),"【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【护理记录】中查找[项目名称]中记录的是{血压},MAP = [(2 × 舒张压) + 收缩压] ÷ 3，用计算出的数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值",,,
ETL_CJ_245,本次需提取,入组时情况_APACHE II评分_HR ○≥180/≤39(+4分) ○140-179/40-54(+3分) ○110-139/55-69(+2分) ○70-109(0分),【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【护理记录】中查找[项目名称]中记录的是{脉搏}，用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,,,
ETL_CJ_246,本次需提取,入组时情况_APACHE II评分_RR ○≥50/≤5(+4分) ○35-49(+3分) ○6-9(+2分) ○25-34/10-11(+1分) ○12-24(0分),【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【护理记录】中查找[项目名称]中记录的是{呼吸}，用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,,,
ETL_CJ_247,本次需提取,入组时情况_APACHE II评分_A-aDO2 (FiO2>0.5) ○≥500(+4分) ○350-499(+3分) ○200-349(+2分) ○<200(0分),【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【血气】中查找[名称]中记录的是{FiO2}对应的{值}≥50%；条件③在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}和精确等于[pCO2]对应的{值}(Fio2的值是百分数，例如取值是100，实际数值应为100%)；条件④A-aD02=713xFi02-PaCO2x1.25-Pa02用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,,,
ETL_CJ_248,本次需提取,入组时情况_APACHE II评分_PaO2 (FiO2<0.5) ○≤55(+4分) ○55-60(+3分) ○61-70(+2分) ○≥70(0分),【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【血气】中查找[名称]中记录的是{FiO2}对应的{值}＜50；条件③条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}；③若有多次测量值，评分选择的数值应采用24小时内最差值,,,
ETL_CJ_249,本次需提取,入组时情况_APACHE II评分_pH动脉血 ○≥7.7/<7.15(+4分) ○7.6-7.69/7.15-7.24(+3分) ○7.25-7.32(+2分) ○7.5-7.59(+1分) ○7.33-7.49(0分),【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}；再将该数值除以与之有相同{血气ID}的{名称}记录{FiO2}的对应的{值}，相除结果数值，用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,,,
ETL_CJ_250,本次需提取,入组时情况_APACHE II评分_Na+ ○≥180/≤110(+4分) ○160-179/111-119(+3分) ○155-159/120-129(+2分) ○150-154(+1分) ○130-149(0分),【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★钠(★Na)}对应的{检验指标值}，用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,,,
ETL_CJ_251,本次需提取,入组时情况_APACHE II评分_K+ ○≥7/<2.5(+4分) ○6-6.9(+3分) ○2.5-2.9(+2分) ○5.5-5.9/3-3.4(+1分) ○3.5-5.4(0分),【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★钾(★K)}对应的{检验指标值}，用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,,,
ETL_CJ_252,本次需提取,入组时情况_APACHE II评分_Cr ○≥309(+4分) ○177-308(+3分) ○133-176/<53(+2分) ○53-132(0分),【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★肌酐（酶法）(★CR)}对应的{检验指标值}，用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,,,
ETL_CJ_253,本次需提取,入组时情况_APACHE II评分_HCT ○≥60/<20(+4分) ○50-59.9/20-29.9(+3分) ○46-49.9(+2分) ○30-45.9(0分),【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★红细胞压积(★HCT)}对应的{检验指标值}，用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,,,
ETL_CJ_254,本次需提取,入组时情况_APACHE II评分_WBC ○≥40/<1(+4分) ○20-39.9/1-2.9(+3分) ○15-19.9(+2分) ○3-14.9(0分),【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★白细胞总数(★WBC)}对应的{检验指标值}，用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,,,
ETL_CJ_255,本次需提取,入组时情况_APACHE II评分_急性生理学评分合计 □□(分),以上12道题得分相加,,,
ETL_CJ_256,本次需提取,入组时情况_APACHE II评分_静脉血HCO3 (mmol/L) ○≥52/<15(+4分) ○41-51.9/15-17.9(+3分) ○32-40.9/18-21.9(+2分) ○22-31.9(0分),"【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{静脉}的，提取与之有相同{血气ID}的{名称}为{cHCO3-(P,st),c}的对应的{值}；用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值",,,
ETL_CJ_257,本次需提取,入组时情况_APACHE II评分_年龄评分 ○≤44(0分) ○45-54(+2分) ○55-64(+3分) ○65-74(+5分) ○≥75(+6分),年龄,,,
ETL_CJ_258,本次需提取,入组时情况_APACHE II评分_总分 □□(分) (①急性生理学评分+②年龄评分+③慢性健康状况评分),【2个评估】条件①[评估时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②提取【2个评估】中{评估项}为{APACHEII}的对应的{值}；条件③若有多个值，选择最差值。,,,
ETL_CJ_259,本次需提取,入组时情况_SOFA评分_呼吸系统_PaO2/FiO2 （mmHg）<br>≥400: 0分<br>< 400: 1分<br><300: 2分<br><200+机械通气: 3分<br><100+机械通气: 4分,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}，再将该数值除以与之有相同{血气ID}的{名称}记录{FiO2}的对应的{值}(Fio2的值是百分数，例如取值是100，实际数值应为100%)③若有多次测量值，评分选择的数值应采用24小时内最差值,,,
ETL_CJ_260,本次需提取,入组时情况_SOFA评分_中枢神经系统_GCS评分<br>15: 0分<br>13-14: 1分<br>10-12: 2分<br>6-9: 3分<br><6: 4分,①【入组时情况_GCS评分_总分】第187行②若有多次测量值，评分选择的数值应采用24小时内最差值,根据【入组时情况_GCS评分_总分】得出选项,,
ETL_CJ_261,本次需提取,入组时情况_SOFA评分_心血管系统 药物剂量[ug/(kg·min)]<br>平均动脉压≥70mmHg: 0分<br>平均动脉压<70mmHg: 1分<br>多巴胺<5或多巴酚丁胺（任何剂量）: 2分<br>多巴胺5.1-15或（去甲）肾上腺素≤0.1: 3分<br>多巴胺>15或（去甲）肾上腺素>0.1: 4分,【入组时情况_SOFA评分_总分】-其他SOFA评分题目的分值，得出本题结果。,收缩压和舒张压从护理系统中获取，然后计算【平均动脉压(MAP) = (收缩压 + 2×舒张压) ÷ 3】,,
ETL_CJ_262,本次需提取,入组时情况_SOFA评分_肝脏系统_胆红素[mg/dl（umol/L）]<br>＜1.2（20）: 0分<br>1.2-1.9（20-32）: 1分<br>2.0-5.9（33-101）: 2分<br>6.0-11.9（102-204）: 3分<br>>12（>204）: 4分,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{总胆红素(TBIL)}对应的{检验指标值}，用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,不是要数值，根据数值计算出选项,,
ETL_CJ_263,本次需提取,入组时情况_SOFA评分_凝血系统_血小板（×10^9/L）<br>≥150: 0分<br><150: 1分<br><100: 2分<br><50: 3分<br><20: 4分,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★血小板(★PLT)}对应的{检验指标值}，用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,不是要数值，根据数值计算出选项,,
ETL_CJ_264,本次需提取,入组时情况_SOFA评分_肾脏系统_肌酐[mg/dl（umol/L）]或尿量（ml/d）<br>＜1.2（110）: 0分<br>1.2-1.9（110-170）: 1分<br>2.0-3.4（171-299）: 2分<br>3.5-4.9（300-440）或< 500ml/d: 3分<br>>5（>440）或< 200ml/d: 4分,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★肌酐（酶法）(★CR)}对应的{检验指标值}*0.07，用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最差值,不是要数值，根据数值计算出选项,,
ETL_CJ_265,本次需提取,入组时情况_SOFA评分_总分,【2个评估】条件①[评估时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②提取【2个评估】中{评估项}为{SOFA}的对应的{值}；条件③若有多个值，选择最差值。,以上6道题得分相加,,
ETL_CJ_266,,入组时情况_CURB-65评分_意识障碍(无=0/有=1),【电子病历】--【现病史】和【病程记录】--【首次病程记录】,"无意识混乱：0分
有意识混乱（例如，定向障碍或急性精神错乱）：1分",,
ETL_CJ_267,,入组时情况_CURB-65评分_血尿素氮＞7mmol/L(无=0/有=1),【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★尿素(★Urea)}对应的{检验指标值}，用此数值得出本题选项和得分；③若有多次测量值，评分选择的数值应采用24小时内最大值，按【正常（≤7 mmol/L）：0分，升高（>7 mmol/L）：1分】输出评分,"正常（≤7 mmol/L）：0分
升高（>7 mmol/L）：1分",,
ETL_CJ_268,,入组时情况_CURB-65评分_呼吸频率≥30次/分(无=0/有=1),【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【护理记录】中查找[项目名称]-呼吸，对应的[项目值(数值)]；③若有多次测量值，评分选择的数值应采用24小时内最大值,"正常（≤24次/分钟）：0分
升高（>24次/分钟）：1分",,
ETL_CJ_269,,入组时情况_CURB-65评分_收缩压＜90mmHg或舒张压≤60mmHg(无=0/有=1),【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]前后48h内；条件②在【护理记录】中查找[项目名称]-血压，对应的[项目值(数值)]；③若有多次测量值，评分选择的数值应采用24小时内最小值,"正常（收缩压≥90 mmHg且舒张压≥60）：0分
低血压（收缩压<90 mmHg或舒张压＜60）：1分",,
ETL_CJ_270,,入组时情况_CURB-65评分_年龄≥65岁(无=0/有=1),病案首页,"年龄小于65岁：0分
年龄65岁及以上：1分",,
ETL_CJ_271,,入组时情况_CURB-65评分_总分,以上5题相加  直接提取,,,
ETL_CJ_272,,入组时情况_监测指标_D1_体温,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【护理记录】中查找[项目名称]-体温，对应的[项目值(数值)]；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_273,,入组时情况_监测指标_D1_舒张压,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【护理记录】中查找[项目名称]-血压，对应的[项目值(数值)]中“/”后的值；③若有多次测量值，选择的数值应采用24小时内最小值,,,
ETL_CJ_274,,入组时情况_监测指标_D1_收缩压,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【护理记录】中查找[项目名称]-血压，对应的[项目值(数值)]中“/”前的值；③若有多次测量值，选择的数值应采用24小时内最小值,,,
ETL_CJ_275,,入组时情况_监测指标_D1_心率,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【护理记录】中查找[项目名称]-[心率]+[脉搏]，对应的[项目值(数值)]；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_276,,入组时情况_监测指标_D1_呼吸频率,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【护理记录】中查找[项目名称]-呼吸，对应的[项目值(数值)]；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_277,,入组时情况_监测指标_D1_SpO2,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【护理记录】中查找[项目名称]-SpO₂，对应的[项目值(字符)]中的值，数值为百分比，需要加上“%”，（例如提取的数值为97，输出结果应为97%）；③若有多次测量值，选择的数值应采用24小时内最小值。,,,
ETL_CJ_278,,入组时情况_监测指标_D1_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机),【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【医嘱】中查找[医嘱名称]中，提取呼吸机使用或呼吸支持治疗的医嘱，按(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)归类；条件③若有多个结果，均输出。,,,
ETL_CJ_279,,入组时情况_监测指标_D1_呼吸支持_吸氧浓度,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【呼吸支持】中查找[名称]-{FiO2}，提取对应的{值}中的数值，若有多组数据，提取最大的数值加上“%”（提取数值100输出结果为100%）。,,,
ETL_CJ_280,,入组时情况_监测指标_D1_呼吸支持_流量,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【入组时情况_监测指标_D1_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（1or2or3）时，在【呼吸支持】中查找与{【入组时情况_监测指标_D1_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果}对应时间的[名称]-{经鼻Flow}and{氧疗Flow}，提取对应的{值}中的数值，若有多组数据，提取最大值。条件③若【入组时情况_监测指标_D1_呼吸支持_支持方式】和或【入组时情况_监测指标_D1_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D1_呼吸支持_支持方式】和或【入组时情况_监测指标_D1_呼吸支持_模式】对应的数值。,,,
ETL_CJ_281,,入组时情况_监测指标_D1_呼吸支持_模式(CPAP=1/ST=2/PSV=3/PAC=4/VAC=5),【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【入组时情况_监测指标_D1_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{Mode}，提取对应的{值}中的文本(包含即可)，与（4：CPAP=1/ST=2）（ST=S/T）（5：PSV=3/PAC=4/VAC=5）进行匹配并选择（4：CPAP=1/ST=2）（5：PSV=3/PAC=4/VAC=5）中的对应数值作为输出结果，若有多个结果，同时输出，但需保留【入组时情况_监测指标_D1_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果的对应性，同时输出【呼吸支持】中的{时间}。,,,
ETL_CJ_282,,入组时情况_监测指标_D1_呼吸支持_参数_呼吸频率,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【入组时情况_监测指标_D1_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{RR}，提取对应的{值}中的数值。若有多组数据，提取最大值。条件③若【入组时情况_监测指标_D1_呼吸支持_支持方式】和或【入组时情况_监测指标_D1_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D1_呼吸支持_支持方式】和或【入组时情况_监测指标_D1_呼吸支持_模式】对应的数值。,,,
ETL_CJ_283,,入组时情况_监测指标_D1_呼吸支持_参数_吸气压力,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【入组时情况_监测指标_D1_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{PS}and{PI}，提取对应的{值}中的文本，若有多组数据，提取最大值。条件③若【入组时情况_监测指标_D1_呼吸支持_支持方式】和或【入组时情况_监测指标_D1_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D1_呼吸支持_支持方式】和或【入组时情况_监测指标_D1_呼吸支持_模式】对应的数值。,,,
ETL_CJ_284,,入组时情况_监测指标_D1_呼吸支持_参数_潮气量,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【入组时情况_监测指标_D1_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{VT}，提取对应的{值}中的数值。若有多组数据，提取最小值。条件③若【入组时情况_监测指标_D1_呼吸支持_支持方式】和或【入组时情况_监测指标_D1_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D1_呼吸支持_支持方式】和或【入组时情况_监测指标_D1_呼吸支持_模式】对应的数值。,,,
ETL_CJ_285,,入组时情况_监测指标_D1_呼吸支持_参数_呼气末正压,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【入组时情况_监测指标_D1_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{PEEP}，提取对应的{值}中的数值。若有多组数据，提取最大值。条件③若【入组时情况_监测指标_D1_呼吸支持_支持方式】和或【入组时情况_监测指标_D1_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D1_呼吸支持_支持方式】和或【入组时情况_监测指标_D1_呼吸支持_模式】对应的数值。,,,
ETL_CJ_286,,入组时情况_监测指标_D1_血气分析_pH,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pH”的记录{pH}的对应的{值}；③若有多次测量值，选择的数值采用距离7.4最远的数值作为结果。,,,
ETL_CJ_287,,入组时情况_监测指标_D1_血气分析_pCO2（mmHg）,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pCO2”的记录{pCO2}的对应的{值}；③若有多次测量值，选择最大值的数值。,,,
ETL_CJ_288,,入组时情况_监测指标_D1_血气分析_pO2（mmHg）,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}；③若有多次测量值，选择最小值的数值。,,,
ETL_CJ_289,,入组时情况_监测指标_D1_血气分析_Lac（mmol/L）,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}的{Lac}的对应的{值}；③若有多次测量值，选择最大的数值。,,,
ETL_CJ_290,,入组时情况_监测指标_D1_血气分析_BE（mmol/L）,"【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}的{cBase(B),c}的对应的{值}；③若有多次测量值，选择绝对值最大值的数值。",,,
ETL_CJ_291,,入组时情况_监测指标_D1_血气分析_PaO2/FiO2,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}；③若有多次测量值，选择最小值的数值，再将该数值除以与之有相同{血气ID}的{名称}记录{FiO2}的对应的{值}(if{Fio2}的值>=21是百分数，例如取值是100，实际数值应为100%；if{Fio2}的值属于（0.21-1）区间则作为小数直接计算，无需“%”规则介入，若取值范围在{0-0.20}，{1.01-20}则记为无效数据，不采用，本次输出结果为‘N/A’)，相除结果小数即为结果。,,,
ETL_CJ_292,,入组时情况_监测指标_D1_CRRT(无=0/有=1),【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【医嘱】中查找[用法]-CRRT，若“有”则记1，“无”记0,,,
ETL_CJ_293,,入组时情况_监测指标_D1_CRRT开始时间,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【医嘱】中查找[用法]-CRRT，若“有”，则记录最早的【开始时间】，输出格式为YYYYMMDD。,,,
ETL_CJ_294,,入组后情况_监测指标_D4_体温,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【护理记录】中查找[项目名称]-体温，对应的[项目值(数值)]；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_295,,入组后情况_监测指标_D4_舒张压,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【护理记录】中查找[项目名称]-血压，对应的[项目值(数值)]中“/”后的值；③若有多次测量值，选择的数值应采用24小时内最小值,,,
ETL_CJ_296,,入组后情况_监测指标_D4_收缩压,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【护理记录】中查找[项目名称]-血压，对应的[项目值(数值)]中“/”前的值；③若有多次测量值，选择的数值应采用24小时内最小值,,,
ETL_CJ_297,,入组后情况_监测指标_D4_心率,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【护理记录】中查找[项目名称]-[心率]+[脉搏]，对应的[项目值(数值)]；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_298,,入组后情况_监测指标_D4_呼吸频率,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【护理记录】中查找[项目名称]-呼吸，对应的[项目值(数值)]；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_299,,入组后情况_监测指标_D4_SpO2,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【护理记录】中查找[项目名称]-SpO₂，对应的[项目值(字符)]中的值，数值为百分比，需要加上“%”，（例如提取的数值为97，输出结果应为97%）；③若有多次测量值，选择的数值应采用24小时内最小值。,,,
ETL_CJ_300,,入组后情况_监测指标_D4_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机),【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【医嘱】中查找[医嘱名称]中，提取呼吸机使用或呼吸支持治疗的医嘱，按(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)归类；条件③若有多个结果，均输出。,,,
ETL_CJ_301,,入组后情况_监测指标_D4_呼吸支持_吸氧浓度,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【呼吸支持】中查找[名称]-{FiO2}，提取对应的{值}中的数值，若有多组数据，提取最大的数值加上“%”（提取数值100输出结果为100%）。,,,
ETL_CJ_302,,入组后情况_监测指标_D4_呼吸支持_流量,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【入组时情况_监测指标_D4_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（1or2or3）时，在【呼吸支持】中查找与{【入组时情况_监测指标_D4_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果}对应时间的[名称]-{经鼻Flow}and{氧疗Flow}，提取对应的{值}中的数值，若有多组数据，提取最大值。条件③若【入组时情况_监测指标_D4_呼吸支持_支持方式】和或【入组时情况_监测指标_D4_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D4_呼吸支持_支持方式】和或【入组时情况_监测指标_D4_呼吸支持_模式】对应的数值。,,,
ETL_CJ_303,,入组后情况_监测指标_D4_呼吸支持_模式(CPAP=1/ST=2/PSV=3/PAC=4/VAC=5),【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【入组时情况_监测指标_D4_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{Mode}，提取对应的{值}中的文本(包含即可)，与（4：CPAP=1/ST=2）（ST=S/T）（5：PSV=3/PAC=4/VAC=5）进行匹配并选择（4：CPAP=1/ST=2）（5：PSV=3/PAC=4/VAC=5）中的对应数值作为输出结果，若有多个结果，同时输出，但需保留【入组时情况_监测指标_D4_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果的对应性，同时输出【呼吸支持】中的{时间}。,,,
ETL_CJ_304,,入组后情况_监测指标_D4_呼吸支持_参数_呼吸频率,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【入组时情况_监测指标_D4_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{RR}，提取对应的{值}中的数值。若有多组数据，提取最大值。条件③若【入组时情况_监测指标_D4_呼吸支持_支持方式】和或【入组时情况_监测指标_D4_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D4_呼吸支持_支持方式】和或【入组时情况_监测指标_D4_呼吸支持_模式】对应的数值。,,,
ETL_CJ_305,,入组后情况_监测指标_D4_呼吸支持_参数_吸气压力,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【入组时情况_监测指标_D4_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{PS}and{PI}，提取对应的{值}中的文本，若有多组数据，提取最大值。条件③若【入组时情况_监测指标_D4_呼吸支持_支持方式】和或【入组时情况_监测指标_D4_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D4_呼吸支持_支持方式】和或【入组时情况_监测指标_D4_呼吸支持_模式】对应的数值。,,,
ETL_CJ_306,,入组后情况_监测指标_D4_呼吸支持_参数_潮气量,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【入组时情况_监测指标_D4_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{VT}，提取对应的{值}中的数值。若有多组数据，提取最小值。条件③若【入组时情况_监测指标_D4_呼吸支持_支持方式】和或【入组时情况_监测指标_D4_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D4_呼吸支持_支持方式】和或【入组时情况_监测指标_D4_呼吸支持_模式】对应的数值。,,,
ETL_CJ_307,,入组后情况_监测指标_D4_呼吸支持_参数_呼气末正压,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【入组时情况_监测指标_D4_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{PEEP}，提取对应的{值}中的数值。若有多组数据，提取最大值。条件③若【入组时情况_监测指标_D4_呼吸支持_支持方式】和或【入组时情况_监测指标_D4_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D4_呼吸支持_支持方式】和或【入组时情况_监测指标_D4_呼吸支持_模式】对应的数值。,,,
ETL_CJ_308,,入组后情况_监测指标_D4_血气分析_pH,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pH”的记录{pH}的对应的{值}；③若有多次测量值，选择的数值采用距离7.4最远的数值作为结果。,,,
ETL_CJ_309,,入组后情况_监测指标_D4_血气分析_pCO2,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pCO2”的记录{pCO2}的对应的{值}；③若有多次测量值，选择最大值的数值。,,,
ETL_CJ_310,,入组后情况_监测指标_D4_血气分析_pO2,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}；③若有多次测量值，选择最小值的数值。,,,
ETL_CJ_311,,入组后情况_监测指标_D4_血气分析_Lac,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}的{Lac}的对应的{值}；③若有多次测量值，选择最大的数值。,,,
ETL_CJ_312,,入组后情况_监测指标_D4_血气分析_BE,"【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}的{cBase(B),c}的对应的{值}；③若有多次测量值，选择绝对值最大值的数值。",,,
ETL_CJ_313,,入组后情况_监测指标_D4_血气分析_PaO2/FiO2,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}；③若有多次测量值，选择最小值的数值，再将该数值除以与之有相同{血气ID}的{名称}记录{FiO2}的对应的{值}(if{Fio2}的值>=21是百分数，例如取值是100，实际数值应为100%；if{Fio2}的值属于（0.21-1）区间则作为小数直接计算，无需“%”规则介入，若取值范围在{0-0.20}，{1.01-20}则记为无效数据，不采用，本次输出结果为‘N/A’)，相除结果小数即为结果。,,,
ETL_CJ_314,,入组后情况_监测指标_D7_体温,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【护理记录】中查找[项目名称]-体温，对应的[项目值(数值)]；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_315,,入组后情况_监测指标_D7_舒张压,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【护理记录】中查找[项目名称]-血压，对应的[项目值(数值)]中“/”后的值；③若有多次测量值，选择的数值应采用24小时内最小值,,,
ETL_CJ_316,,入组后情况_监测指标_D7_收缩压,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【护理记录】中查找[项目名称]-血压，对应的[项目值(数值)]中“/”前的值；③若有多次测量值，选择的数值应采用24小时内最小值,,,
ETL_CJ_317,,入组后情况_监测指标_D7_心率,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【护理记录】中查找[项目名称]-[心率]+[脉搏]，对应的[项目值(数值)]；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_318,,入组后情况_监测指标_D7_呼吸频率,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【护理记录】中查找[项目名称]-呼吸，对应的[项目值(数值)]；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_319,,入组后情况_监测指标_D7_SpO2,【护理记录】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【护理记录】中查找[项目名称]-SpO₂，对应的[项目值(字符)]中的值，数值为百分比，需要加上“%”，（例如提取的数值为97，输出结果应为97%）；③若有多次测量值，选择的数值应采用24小时内最小值。,,,
ETL_CJ_320,,入组后情况_监测指标_D7_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机),【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【医嘱】中查找[医嘱名称]中，提取呼吸机使用或呼吸支持治疗的医嘱，按(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)归类；条件③若有多个结果，均输出。,,,
ETL_CJ_321,,入组后情况_监测指标_D7_呼吸支持_吸氧浓度,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【呼吸支持】中查找[名称]-{FiO2}，提取对应的{值}中的数值，若有多组数据，提取最大的数值加上“%”（提取数值100输出结果为100%）。,,,
ETL_CJ_322,,入组后情况_监测指标_D7_呼吸支持_流量,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【入组时情况_监测指标_D7_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（1or2or3）时，在【呼吸支持】中查找与{【入组时情况_监测指标_D7_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果}对应时间的[名称]-{经鼻Flow}and{氧疗Flow}，提取对应的{值}中的数值，若有多组数据，提取最大值。条件③若【入组时情况_监测指标_D7_呼吸支持_支持方式】和或【入组时情况_监测指标_D7_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D7_呼吸支持_支持方式】和或【入组时情况_监测指标_D7_呼吸支持_模式】对应的数值。,,,
ETL_CJ_323,,入组后情况_监测指标_D7_呼吸支持_模式(CPAP=1/ST=2/PSV=3/PAC=4/VAC=5),【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【入组时情况_监测指标_D7_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{Mode}，提取对应的{值}中的文本(包含即可)，与（4：CPAP=1/ST=2）（ST=S/T）（5：PSV=3/PAC=4/VAC=5）进行匹配并选择（4：CPAP=1/ST=2）（5：PSV=3/PAC=4/VAC=5）中的对应数值作为输出结果，若有多个结果，同时输出，但需保留【入组时情况_监测指标_D7_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果的对应性，同时输出【呼吸支持】中的{时间}。,,,
ETL_CJ_324,,入组后情况_监测指标_D7_呼吸支持_参数_呼吸频率,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【入组时情况_监测指标_D7_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{RR}，提取对应的{值}中的数值。若有多组数据，提取最大值。条件③若【入组时情况_监测指标_D7_呼吸支持_支持方式】和或【入组时情况_监测指标_D7_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D7_呼吸支持_支持方式】和或【入组时情况_监测指标_D7_呼吸支持_模式】对应的数值。,,,
ETL_CJ_325,,入组后情况_监测指标_D7_呼吸支持_参数_吸气压力,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【入组时情况_监测指标_D7_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{PS}and{PI}，提取对应的{值}中的文本，若有多组数据，提取最大值。条件③若【入组时情况_监测指标_D7_呼吸支持_支持方式】和或【入组时情况_监测指标_D7_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D7_呼吸支持_支持方式】和或【入组时情况_监测指标_D7_呼吸支持_模式】对应的数值。,,,
ETL_CJ_326,,入组后情况_监测指标_D7_呼吸支持_参数_潮气量,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【入组时情况_监测指标_D7_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{VT}，提取对应的{值}中的数值。若有多组数据，提取最小值。条件③若【入组时情况_监测指标_D7_呼吸支持_支持方式】和或【入组时情况_监测指标_D7_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D7_呼吸支持_支持方式】和或【入组时情况_监测指标_D7_呼吸支持_模式】对应的数值。,,,
ETL_CJ_327,,入组后情况_监测指标_D7_呼吸支持_参数_呼气末正压,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【入组时情况_监测指标_D7_呼吸支持_支持方式(1=鼻导管/2=面罩/3=经鼻高流量/4=无创呼吸机/5=有创呼吸机)】中结果为（4or5）时，在【呼吸支持】中查找[名称]-{PEEP}，提取对应的{值}中的数值。若有多组数据，提取最大值。条件③若【入组时情况_监测指标_D7_呼吸支持_支持方式】和或【入组时情况_监测指标_D7_呼吸支持_模式】有多个结果，取与之【入组时情况_监测指标_D7_呼吸支持_支持方式】和或【入组时情况_监测指标_D7_呼吸支持_模式】对应的数值。,,,
ETL_CJ_328,,入组后情况_监测指标_D7_血气分析_pH,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pH”的记录{pH}的对应的{值}；③若有多次测量值，选择的数值采用距离7.4最远的数值作为结果。,,,
ETL_CJ_329,,入组后情况_监测指标_D7_血气分析_pCO2（mmHg）,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pCO2”的记录{pCO2}的对应的{值}；③若有多次测量值，选择最大值的数值。,,,
ETL_CJ_330,,入组后情况_监测指标_D7_血气分析_pO2（mmHg）,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}；③若有多次测量值，选择最小值的数值。,,,
ETL_CJ_331,,入组后情况_监测指标_D7_血气分析_Lac（mmol/L）,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}的{Lac}的对应的{值}；③若有多次测量值，选择最大的数值。,,,
ETL_CJ_332,,入组后情况_监测指标_D7_血气分析_BE（mmol/L）,"【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}的{cBase(B),c}的对应的{值}；③若有多次测量值，选择绝对值最大值的数值。",,,
ETL_CJ_333,,入组后情况_监测指标_D7_血气分析_PaO2/FiO2,【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}；③若有多次测量值，选择最小值的数值，再将该数值除以与之有相同{血气ID}的{名称}记录{FiO2}的对应的{值}(if{Fio2}的值>=21是百分数，例如取值是100，实际数值应为100%；if{Fio2}的值属于（0.21-1）区间则作为小数直接计算，无需“%”规则介入，若取值范围在{0-0.20}，{1.01-20}则记为无效数据，不采用，本次输出结果为‘N/A’)，相除结果小数即为结果。,,,
ETL_CJ_334,,检验指标_D1_感染及炎症指标_WBC（109/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★白细胞总数(★WBC)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_335,,检验指标_D1_感染及炎症指标_NE（109/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{中性粒细胞总数(NEUT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_336,,检验指标_D1_感染及炎症指标_LY（109/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{淋巴细胞总数(LYMPH)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_337,,检验指标_D1_感染及炎症指标_PLT（109/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★血小板(★PLT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内【最大值和最小值，用“/”隔开。】,,,
ETL_CJ_338,,检验指标_D1_感染及炎症指标_Hb（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★血红蛋白(★HGB)}对应的{检验指标值，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值。,,,
ETL_CJ_339,,检验指标_D1_感染及炎症指标_CRP（mg/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{超敏C-反应蛋白(hs-CRP)}+{*C反应蛋白(*CRP)}对应的{检验指标值}同时提取，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_340,,检验指标_D1_感染及炎症指标_PCT（ng/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{全血降钙素原(PCT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_341,,检验指标_D1_肝肾功能_ALB（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★白蛋白定量(BCG法)(★ALB)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_342,,检验指标_D1_肝肾功能_ALT（U/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★丙氨酸氨基转移酶(★ALT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_343,,检验指标_D1_肝肾功能_AST（U/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★天冬氨酸氨基转移酶(★AST)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_344,,检验指标_D1_肝肾功能_TBIL（μmol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*总胆红素(*TBIL)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_345,,检验指标_D1_肝肾功能_BUN（mmol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★尿素(★Urea)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_346,,检验指标_D1_肝肾功能_Crea（μmol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★肌酐（酶法）(★CR)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_347,,检验指标_D1_心功能_cTNI（ng/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*高敏肌钙蛋白I(*hs-cTnl)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_348,,检验指标_D1_心功能_cTNT（ng/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{高敏肌钙蛋白T(hs-cTnT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_349,,检验指标_D1_心功能_CK-MB（ng/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{肌酸激酶-MB同工酶(CK-MBmass)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_350,,检验指标_D1_心功能_BNP（pg/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{B型钠尿肽(BNP)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_351,,检验指标_D1_心功能_NT-proBNP,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{N端-B型钠尿肽前体(NT-ProBNP)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_352,,检验指标_D1_凝血功能_PT（s）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★凝血酶原时间(★PT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_353,,检验指标_D1_凝血功能_APTT（s）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*活化部分凝血活酶时间(*APTT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_354,,检验指标_D1_凝血功能_PTA（%）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{凝血酶原活动度(PTA)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_355,,检验指标_D1_凝血功能_D-D（mg/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*D-二聚体定量(*D-D)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_356,,检验指标_D1_凝血功能_Fib（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*纤维蛋白原定量测定(*Fib)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_357,,检验指标_D1_免疫状态_CD4绝对值（cells/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{CD3+CD4+T细胞绝对计数(CD4+)}对应的{检验指标值}，提取数值后*10^6；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_358,,检验指标_D1_免疫状态_CD8绝对值（cells/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{CD3+CD8+T细胞绝对计数(CD8+)}对应的{检验指标值}，提取数值后*10^6；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_359,,检验指标_D1_免疫状态_B细胞绝对值（cells/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{CD19+B细胞绝对计数(CD19+)}and{B细胞绝对计数}对应的{检验指标值}，提取数值；③若有多次测量值，或同时在{CD19+B细胞绝对计数(CD19+)}and{B细胞绝对计数}对应的{检验指标值}提取到数值，选择的数值应采用24小时内最大值,,,
ETL_CJ_360,,检验指标_D1_免疫状态_NK绝对值（cells/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{NK细胞绝对计数(NK)}对应的{检验指标值}，提取数值后*10^6；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_361,,检验指标_D1_免疫状态_C3（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*补体C3测定(*C3)}对应的{检验指标值}，提取数值*0.01；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_362,,检验指标_D1_免疫状态_C4（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*补体C4测定(*C4)}对应的{检验指标值}，提取数值*0.01；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_363,,检验指标_D1_免疫状态_IgG（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★免疫球蛋白G(★IgG)}对应的{检验指标值}，提取数值*0.07；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_364,,检验指标_D1_血脂_CHO（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★总胆固醇(★TC)}对应的{检验指标值}，提取数值*10^3；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_365,,检验指标_D1_血脂_TG（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★甘油三酯(★TG)}对应的{检验指标值}，提取数值*10^3；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_366,,检验指标_D1_血脂_LDL（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★低密度脂蛋白胆固醇(★LDL-C)}对应的{检验指标值}，提取数值*10^3；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_367,,检验指标_D1_血脂_HDL（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★高密度脂蛋白胆固醇(★HDL-C)}对应的{检验指标值}，提取数值*10^3；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_368,,检验指标_D1_炎症指标_IL-6,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]前后24h内；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*白介素-6(*IL-6)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_369,,检验指标_D4_感染及炎症指标_WBC（109/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★白细胞总数(★WBC)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_370,,检验指标_D4_感染及炎症指标_NE（109/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{中性粒细胞总数(NEUT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_371,,检验指标_D4_感染及炎症指标_LY（109/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{淋巴细胞总数(LYMPH)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_372,,检验指标_D4_感染及炎症指标_PLT（109/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★血小板(★PLT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内【最大值和最小值，用“/”隔开。】,,,
ETL_CJ_373,,检验指标_D4_感染及炎症指标_Hb（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★血红蛋白(★HGB)}对应的{检验指标值，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值。,,,
ETL_CJ_374,,检验指标_D4_感染及炎症指标_CRP（mg/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{超敏C-反应蛋白(hs-CRP)}+{*C反应蛋白(*CRP)}对应的{检验指标值}同时提取，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_375,,检验指标_D4_感染及炎症指标_PCT（ng/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{全血降钙素原(PCT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_376,,检验指标_D4_肝肾功能_ALB（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★白蛋白定量(BCG法)(★ALB)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_377,,检验指标_D4_肝肾功能_ALT（U/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★丙氨酸氨基转移酶(★ALT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_378,,检验指标_D4_肝肾功能_AST（U/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★天冬氨酸氨基转移酶(★AST)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_379,,检验指标_D4_肝肾功能_TBIL（μmol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*总胆红素(*TBIL)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_380,,检验指标_D4_肝肾功能_BUN（mmol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★尿素(★Urea)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_381,,检验指标_D4_肝肾功能_Crea（μmol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★肌酐（酶法）(★CR)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_382,,检验指标_D4_心功能_cTNI（ng/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*高敏肌钙蛋白I(*hs-cTnl)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_383,,检验指标_D4_心功能_cTNT（ng/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{高敏肌钙蛋白T(hs-cTnT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_384,,检验指标_D4_心功能_CK-MB（ng/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{肌酸激酶-MB同工酶(CK-MBmass)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_385,,检验指标_D4_心功能_BNP（pg/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{B型钠尿肽(BNP)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_386,,检验指标_D4_心功能_NT-proBNP,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{N端-B型钠尿肽前体(NT-ProBNP)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_387,,检验指标_D4_凝血功能_PT（s）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★凝血酶原时间(★PT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_388,,检验指标_D4_凝血功能_APTT（s）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*活化部分凝血活酶时间(*APTT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_389,,检验指标_D4_凝血功能_PTA（%）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{凝血酶原活动度(PTA)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_390,,检验指标_D4_凝血功能_D-D（mg/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*D-二聚体定量(*D-D)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_391,,检验指标_D4_凝血功能_Fib（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*纤维蛋白原定量测定(*Fib)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_392,,检验指标_D4_免疫状态_CD4绝对值（cells/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{CD3+CD4+T细胞绝对计数(CD4+)}对应的{检验指标值}，提取数值后*10^6；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_393,,检验指标_D4_免疫状态_CD8绝对值（cells/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{CD3+CD8+T细胞绝对计数(CD8+)}对应的{检验指标值}，提取数值后*10^6；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_394,,检验指标_D4_免疫状态_B细胞绝对值（cells/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{CD19+B细胞绝对计数(CD19+)}and{B细胞绝对计数}对应的{检验指标值}，提取数值；③若有多次测量值，或同时在{CD19+B细胞绝对计数(CD19+)}and{B细胞绝对计数}对应的{检验指标值}提取到数值，选择的数值应采用24小时内最大值,,,
ETL_CJ_395,,检验指标_D4_免疫状态_NK绝对值（cells/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{NK细胞绝对计数(NK)}对应的{检验指标值}，提取数值后*10^6；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_396,,检验指标_D4_免疫状态_C3（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*补体C3测定(*C3)}对应的{检验指标值}，提取数值*0.01；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_397,,检验指标_D4_免疫状态_C4（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*补体C4测定(*C4)}对应的{检验指标值}，提取数值*0.01；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_398,,检验指标_D4_免疫状态_IgG（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★免疫球蛋白G(★IgG)}对应的{检验指标值}，提取数值*0.07；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_399,,检验指标_D4_血脂_CHO（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★总胆固醇(★TC)}对应的{检验指标值}，提取数值*10^3；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_400,,检验指标_D4_血脂_TG（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★甘油三酯(★TG)}对应的{检验指标值}，提取数值*10^3；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_401,,检验指标_D4_血脂_LDL（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★低密度脂蛋白胆固醇(★LDL-C)}对应的{检验指标值}，提取数值*10^3；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_402,,检验指标_D4_血脂_HDL（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★高密度脂蛋白胆固醇(★HDL-C)}对应的{检验指标值}，提取数值*10^3；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_403,,检验指标_D4_炎症指标_IL-6,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内，提取最接近“第4天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*白介素-6(*IL-6)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_404,,检验指标_D7_感染及炎症指标_WBC（109/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★白细胞总数(★WBC)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_405,,检验指标_D7_感染及炎症指标_NE（109/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{中性粒细胞总数(NEUT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_406,,检验指标_D7_感染及炎症指标_LY（109/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{淋巴细胞总数(LYMPH)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_407,,检验指标_D7_感染及炎症指标_PLT（109/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★血小板(★PLT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内【最大值和最小值，用“/”隔开】。,,,
ETL_CJ_408,,检验指标_D7_感染及炎症指标_Hb（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★血红蛋白(★HGB)}对应的{检验指标值，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值。,,,
ETL_CJ_409,,检验指标_D7_感染及炎症指标_CRP（mg/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{超敏C-反应蛋白(hs-CRP)}+{*C反应蛋白(*CRP)}对应的{检验指标值}同时提取，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_410,,检验指标_D7_感染及炎症指标_PCT（ng/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{全血降钙素原(PCT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_411,,检验指标_D7_肝肾功能_ALB（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★白蛋白定量(BCG法)(★ALB)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_412,,检验指标_D7_肝肾功能_ALT（U/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★丙氨酸氨基转移酶(★ALT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_413,,检验指标_D7_肝肾功能_AST（U/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★天冬氨酸氨基转移酶(★AST)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_414,,检验指标_D7_肝肾功能_TBIL（μmol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*总胆红素(*TBIL)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_415,,检验指标_D7_肝肾功能_BUN（mmol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★尿素(★Urea)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_416,,检验指标_D7_肝肾功能_Crea（μmol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★肌酐（酶法）(★CR)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_417,,检验指标_D7_心功能_cTNI（ng/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*高敏肌钙蛋白I(*hs-cTnl)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_418,,检验指标_D7_心功能_cTNT（ng/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{高敏肌钙蛋白T(hs-cTnT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_419,,检验指标_D7_心功能_CK-MB（ng/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{肌酸激酶-MB同工酶(CK-MBmass)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_420,,检验指标_D7_心功能_BNP（pg/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{B型钠尿肽(BNP)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_421,,检验指标_D7_心功能_NT-proBNP,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{N端-B型钠尿肽前体(NT-ProBNP)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_422,,检验指标_D7_凝血功能_PT（s）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★凝血酶原时间(★PT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_423,,检验指标_D7_凝血功能_APTT（s）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*活化部分凝血活酶时间(*APTT)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_424,,检验指标_D7_凝血功能_PTA（%）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{凝血酶原活动度(PTA)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_425,,检验指标_D7_凝血功能_D-D（mg/ml）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*D-二聚体定量(*D-D)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_426,,检验指标_D7_凝血功能_Fib（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*纤维蛋白原定量测定(*Fib)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_427,,检验指标_D7_免疫状态_CD4绝对值（cells/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{CD3+CD4+T细胞绝对计数(CD4+)}对应的{检验指标值}，提取数值后*10^6；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_428,,检验指标_D7_免疫状态_CD8绝对值（cells/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{CD3+CD8+T细胞绝对计数(CD8+)}对应的{检验指标值}，提取数值后*10^6；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_429,,检验指标_D7_免疫状态_B细胞绝对值（cells/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{CD19+B细胞绝对计数(CD19+)}and{B细胞绝对计数}对应的{检验指标值}，提取数值；③若有多次测量值，或同时在{CD19+B细胞绝对计数(CD19+)}and{B细胞绝对计数}对应的{检验指标值}提取到数值，选择的数值应采用24小时内最大值,,,
ETL_CJ_430,,检验指标_D7_免疫状态_NK绝对值（cells/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{NK细胞绝对计数(NK)}对应的{检验指标值}，提取数值后*10^6；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_431,,检验指标_D7_免疫状态_C3（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*补体C3测定(*C3)}对应的{检验指标值}，提取数值*0.01；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_432,,检验指标_D7_免疫状态_C4（g/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*补体C4测定(*C4)}对应的{检验指标值}，提取数值*0.01；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_433,,检验指标_D7_免疫状态_IgG（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★免疫球蛋白G(★IgG)}对应的{检验指标值}，提取数值*0.07；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_434,,检验指标_D7_血脂_CHO（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★总胆固醇(★TC)}对应的{检验指标值}，提取数值*10^3；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_435,,检验指标_D7_血脂_TG（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★甘油三酯(★TG)}对应的{检验指标值}，提取数值*10^3；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_436,,检验指标_D7_血脂_LDL（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★低密度脂蛋白胆固醇(★LDL-C)}对应的{检验指标值}，提取数值*10^3；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_437,,检验指标_D7_血脂_HDL（umol/L）,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★高密度脂蛋白胆固醇(★HDL-C)}对应的{检验指标值}，提取数值*10^3；③若有多次测量值，选择的数值应应采用24小时内最小值,,,
ETL_CJ_438,,检验指标_D7_炎症指标_IL-6,【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内，提取最接近“第7天”08：00am的结果；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{*白介素-6(*IL-6)}对应的{检验指标值}，提取数值；③若有多次测量值，选择的数值应采用24小时内最大值,,,
ETL_CJ_439,,抗感染药物使用_1_药物名称,"目标： 从医疗记录中提取患者在特定时间段内使用的抗感染药物信息，并将其标准化为通用名。
条件：
①抗感染药物识别与标准化：
1.触发时间： 在【病案首页】中的【重症监护室进入时间1】的日期时间之后。
2.识别字段： 【医嘱】中的 {医嘱名称} 字段。
3.识别规则： 识别 {医嘱名称} 内的用药医嘱是否包含以下任一药物名称（包括商品名）： 
1.一代头孢菌素：头孢氨苄、头孢拉啶、头孢羟氨苄、头孢唑啉、头孢硫脒
2.二代头孢菌素：头孢克洛、头孢呋辛、头孢替安、头孢丙烯、头孢孟多
3.三代头孢菌素：头孢克肟、头孢泊肟、头孢他啶、头孢曲松、头孢噻肟
4.四代头孢菌素：头孢吡肟、头孢匹罗
5.青霉素类：阿莫西林、青霉素、氨苄西林、阿洛西林、美洛西林、苯唑西林
6.头霉素/氧头孢烯类：头孢西丁、头孢美唑、头孢米诺、氧头孢、拉氧头孢、氟氧头孢
7.氨基糖苷类：阿米卡星、庆大霉素、妥布霉素、依替米星、异帕米星、链霉素、卡那霉素
8.大环内酯类：阿奇霉素、克拉霉素、红霉素
9.喹诺酮类：莫西沙星、左氧氟沙星、环丙沙星、取伐沙星、帕珠沙星、司帕沙星
10.四环素类（不含替加环素）：米诺环素、多西环素、四环素
11.β内酰胺酶抑制剂复方制剂：头孢哌酮/舒巴坦、哌拉西林/舒巴坦、哌拉西林/他唑巴坦、氨苄西林/舒巴坦、阿莫西林/克拉维酸、替卡西林/克拉维酸、头孢他啶/阿维巴坦
12.糖肽类：万古霉素、替考拉宁
13.唑烷酮类：利奈唑胺
14.环酯肽类：达托霉素
15.碳青霉烯类：亚胺培南、美罗培南、厄他培南、比阿培南、帕尼培南
16.硝基咪唑类：甲硝唑、替硝唑、奥硝唑
17.磺胺类：磺胺甲恶唑
4.标准化输出： 对于识别到的药物，务必 使用提供的商品名与通用名映射表将其标准化为通用名进行输出。
5.映射规则： 
1.优先查找映射表中是否存在与 {医嘱名称}匹配的商品名。
2.如果找到匹配的商品名，则输出其对应的通用名。
3.特殊情况： 如果 {医嘱名称} 中的药物名称在提供的映射表中没有找到对应的条目，则直接输出{医嘱名称}中的原始文本（即商品名）。
6.商品名通用名映射表：
 {1. 第一代头孢菌素 头孢氨苄 先锋4号、美丰、申嘉、斯宝力克、福林、抗先那 
头孢拉定 先锋6号、泛捷复、君必青、克必力、赛菲得 
头孢羟氨苄 力欣奇、欧意、赛锋、仙逢久 
头孢唑林 先锋5号、新泰林、赛福宁（注射用）、凯复卓 
头孢硫脒 仙力素（独家品种） 
2.第二代头孢菌素 头孢克洛 希刻劳（原研）、可福乐、欣可诺、优克诺 
头孢呋辛 西力欣（注射）、达力新（口服）、伏乐新、明可欣 
头孢替安 佩罗欣、海替舒 
头孢丙烯 施复捷（原研）、银力舒 
头孢孟多 猛多力 
3. 第三代头孢菌素 头孢克肟 世福素（原研）、达力芬、奥威素 
头孢泊肟酯 博拿（原研）、施博 
头孢他啶 复达欣（原研）、凯复定、新天欣 
头孢曲松 罗氏芬（原研）、泛生舒复、丽珠芬 
头孢噻肟 凯帝龙、治菌必妥 
4. 第四代头孢菌素  头孢吡肟 马斯平（原研）、信立威 
头孢匹罗 派新 
5. 青霉素类 阿莫 西林 阿莫仙、再林、弗莱莫星 
青霉素 青霉素G钠（华北制药等） 
氨苄西林 安必仙 
美洛西林 天林 
苯唑西林 新青II号 
6. 头霉素/氧头孢烯类 头孢西丁 美福仙 
头孢美唑 先锋美他醇 
拉氧头孢 噻马灵 
7. 氨基糖苷类 阿米卡星 丁胺卡那霉素（注射用） 
庆大霉素 瑞贝克（口服）、艮他霉素 
妥布霉素 托百士（滴眼液）、乃柏欣 
8. 大环内酯类 阿奇霉素 希舒美（原研）、维宏、津博 
克拉霉素 克拉仙（原研）、诺邦 
红霉素 威霉素（肠溶片）、艾狄密新 
9. 喹诺酮类 莫西沙星 拜复乐（原研）、威莫星 
左氧氟沙星 可乐必妥（原研）、利复星 
环丙沙星 西普乐（原研）、悉复欢 
10. 四环素类 米诺环素 美满霉素（原研）、玫满 
多西环素 福多力、永喜 
四环素 四环素碱（原料药） 
β内酰胺类+酶抑制剂  头孢哌酮/舒巴坦 舒普深、铃兰欣、先普、威特神、斯坦定 
哌拉西林/他唑巴坦 特治星（原研）、邦达、联邦他唑仙、康得力、锋泰灵 
阿莫西林/克拉维酸 君尔清、安灭菌（Augmentin）、力百汀、艾克儿、诺可 
氨苄西林/舒巴坦 优立新（Unasyn）、舒他西林、强力安必仙 
替卡西林/克拉维酸 特美汀（Timentin） 
头孢他啶/阿维巴坦 思福妥（Zavicefta，进口原研） 
糖肽类 万古霉素 稳可信（原研）、方刻林、来可信 
替考拉宁 他格适（原研）、加立信、安信 
唑烷酮类 利奈唑胺 斯沃（原研）、齐佶、易瑞达 
环酯肽类 达托霉素 克必信（原研）、多立克 
碳青霉烯类 亚胺培南 泰能（Tienam，含西司他丁）、齐佩能 
美罗培南 美平（Mepem）、倍能、海正美特 
厄他培南 怡万之（Invanz） 
比阿培南 安信（国产首仿） 
帕尼培南 克倍宁（Carbenin，日本三共） 
硝基咪唑类 甲硝唑 灭滴灵、佳尔钠、弗来格 
替硝唑 济得、服净、希普宁 
奥硝唑 圣诺安、优伦、齐克 
磺胺类 磺胺甲恶唑 新诺明（SMZ）、百炎净（复方新诺明） }
②多重用药记录：
1.如果患者使用了多种符合条件的抗感染药物，则按照其使用的时间顺序，依次填入预设的字段中，例如【抗感染药物使用_1_药物名称】、【抗感染药物使用_2_药物名称】……【抗感染药物使用_6_药物名称】。
2.时间的判断依据为【医嘱】中的开始时间。
③药物洗脱判断：
1.对于同一种抗感染药物（以标准化后的通用名为准），如果两次医嘱使用的时间间隔超过24小时（以上一次医嘱的结束时间和下一次医嘱的开始时间计算），则认为该药物发生洗脱。
2.洗脱后再次使用的该药物，将被视为下一次不同的用药，并记录在下一个【抗感染药物使用_x_药物名称】字段中。
④无效医嘱排除：
1.如果识别到【医嘱】中 {用法} 给药途径为静脉途径类的医嘱，并且该医嘱记录的 {开始时间} 与 {结束时间} 之间的间隔小于等于10分钟，则该条医嘱被认为是无效医嘱，不应记录。
2.【医嘱】中 {用法} 给药途径为皮内注射的算作皮试，不记录。
⑤剂量与频次变化处理：
1.如果同一种药物（以标准化后的通用名为准）的剂量或每日给药频次发生改变，则视为两种不同的用药，将被视为下一次不同的用药，并记录在下一个【抗感染药物使用_x_药物名称】字段中。",,,
ETL_CJ_440,,抗感染药物使用_1_剂量,"【医嘱】条件①成功提取【抗感染药物使用_1_药物名称】后，提取【抗感染药物使用_1_药物名称】对应的{剂量}数值出来，填入；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱。条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_441,,抗感染药物使用_1_用药频率,"【医嘱】条件①成功提取【抗感染药物使用_1_药物名称】后，提取【抗感染药物使用_1_药物名称】对应的{频次}后，输出（ ）次/每日，的数值，（例，“间隔8小时”，则输出“3”）；条件②{频次}内容为【立即执行】时，忽略；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_442,,抗感染药物使用_1_开始时间,"【医嘱】条件①成功提取【抗感染药物使用_1_药物名称】后；条件②提取【抗感染药物使用_1_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最早的{开始时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_443,,抗感染药物使用_1_结束时间,"【医嘱】条件①成功提取【抗感染药物使用_1_药物名称】后；条件②提取【抗感染药物使用_1_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最后一次该药物的用药医嘱{结束时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_444,,抗感染药物使用_1_给药途径(口服=1/静脉=2/雾化=3),"【医嘱】条件①成功提取【抗感染药物使用_1_药物名称】后；提取【抗感染药物使用_1_药物名称】对应的{用法}并归类为（口服=1/静脉=2/雾化=3）；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱；条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_445,,抗感染药物使用_2_药物名称,同【抗感染药物使用_1_药物名称】,,,
ETL_CJ_446,,抗感染药物使用_2_剂量,"【医嘱】条件①成功提取【抗感染药物使用_2_药物名称】后，提取【抗感染药物使用_2_药物名称】对应的{剂量}数值出来，填入；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱。条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_447,,抗感染药物使用_2_用药频率,"【医嘱】条件①成功提取【抗感染药物使用_2_药物名称】后，提取【抗感染药物使用_2_药物名称】对应的{频次}，后输出（ ）次/每日，的数值，（例，“间隔8小时”，则输出“3”）；条件②{频次}内容为【立即执行】时，忽略；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_448,,抗感染药物使用_2_开始时间,"【医嘱】条件①成功提取【抗感染药物使用_2_药物名称】后；条件②提取【抗感染药物使用_2_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最早的{开始时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_449,,抗感染药物使用_2_结束时间,"【医嘱】条件①成功提取【抗感染药物使用_2_药物名称】后；条件②提取【抗感染药物使用_2_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最后一次该药物的用药医嘱{结束时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_450,,抗感染药物使用_2_给药途径(口服=1/静脉=2/雾化=3),"【医嘱】条件①成功提取【抗感染药物使用_2_药物名称】后；提取【抗感染药物使用_2_药物名称】对应的{用法}并归类为（口服=1/静脉=2/雾化=3）；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱；条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_451,,抗感染药物使用_3_药物名称,同【抗感染药物使用_1_药物名称】,,,
ETL_CJ_452,,抗感染药物使用_3_剂量,"【医嘱】条件①成功提取【抗感染药物使用_3_药物名称】后，提取【抗感染药物使用_3_药物名称】对应的{剂量}数值出来，填入；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱。条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_453,,抗感染药物使用_3_用药频率,"【医嘱】条件①成功提取【抗感染药物使用_3_药物名称】后，提取【抗感染药物使用_3_药物名称】对应的{频次}，后输出（ ）次/每日，的数值，（例，“间隔8小时”，则输出“3”）；条件②{频次}内容为【立即执行】时，忽略；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_454,,抗感染药物使用_3_开始时间,"【医嘱】条件①成功提取【抗感染药物使用_3_药物名称】后；条件②提取【抗感染药物使用_3_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最早的{开始时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_455,,抗感染药物使用_3_结束时间,"【医嘱】条件①成功提取【抗感染药物使用_3_药物名称】后；条件②提取【抗感染药物使用_3_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最后一次该药物的用药医嘱{结束时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_456,,抗感染药物使用_3_给药途径(口服=1/静脉=2/雾化=3),"【医嘱】条件①成功提取【抗感染药物使用_3_药物名称】后；提取【抗感染药物使用_3_药物名称】对应的{用法}并归类为（口服=1/静脉=2/雾化=3）；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱；条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_457,,抗感染药物使用_4_药物名称,同【抗感染药物使用_1_药物名称】,,,
ETL_CJ_458,,抗感染药物使用_4_剂量,"【医嘱】条件①成功提取【抗感染药物使用_4_药物名称】后，提取【抗感染药物使用_4_药物名称】对应的{剂量}数值出来，填入；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱。条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_459,,抗感染药物使用_4_用药频率,"【医嘱】条件①成功提取【抗感染药物使用_4_药物名称】后，提取【抗感染药物使用_4_药物名称】对应的{频次}，后输出（ ）次/每日，的数值，（例，“间隔8小时”，则输出“3”）；条件②{频次}内容为【立即执行】时，忽略；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_460,,抗感染药物使用_4_开始时间,"【医嘱】条件①成功提取【抗感染药物使用_4_药物名称】后；条件②提取【抗感染药物使用_4_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最早的{开始时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_461,,抗感染药物使用_4_结束时间,"【医嘱】条件①成功提取【抗感染药物使用_4_药物名称】后；条件②提取【抗感染药物使用_4_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最后一次该药物的用药医嘱{结束时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_462,,抗感染药物使用_4_给药途径(口服=1/静脉=2/雾化=3),"【医嘱】条件①成功提取【抗感染药物使用_4_药物名称】后；提取【抗感染药物使用_4_药物名称】对应的{用法}并归类为（口服=1/静脉=2/雾化=3）；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱；条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_463,,抗感染药物使用_5_药物名称,同【抗感染药物使用_1_药物名称】,,,
ETL_CJ_464,,抗感染药物使用_5_剂量,"【医嘱】条件①成功提取【抗感染药物使用_5_药物名称】后，提取【抗感染药物使用_5_药物名称】对应的{剂量}数值出来，填入；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱。条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_465,,抗感染药物使用_5_用药频率,"【医嘱】条件①成功提取【抗感染药物使用_5_药物名称】后，提取【抗感染药物使用_5_药物名称】对应的{频次}，后输出（ ）次/每日，的数值，（例，“间隔8小时”，则输出“3”）；条件②{频次}内容为【立即执行】时，忽略；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_466,,抗感染药物使用_5_开始时间,"【医嘱】条件①成功提取【抗感染药物使用_5_药物名称】后；条件②提取【抗感染药物使用_5_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最早的{开始时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_467,,抗感染药物使用_5_结束时间,"【医嘱】条件①成功提取【抗感染药物使用_5_药物名称】后；条件②提取【抗感染药物使用_5_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最后一次该药物的用药医嘱{结束时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_468,,抗感染药物使用_5_给药途径(口服=1/静脉=2/雾化=3),"【医嘱】条件①成功提取【抗感染药物使用_5_药物名称】后；提取【抗感染药物使用_5_药物名称】对应的{用法}并归类为（口服=1/静脉=2/雾化=3）；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱；条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_469,,抗感染药物使用_6_药物名称,同【抗感染药物使用_1_药物名称】,,,
ETL_CJ_470,,抗感染药物使用_6_剂量,"【医嘱】条件①成功提取【抗感染药物使用_6_药物名称】后，提取【抗感染药物使用_6_药物名称】对应的{剂量}数值出来，填入；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱。条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_471,,抗感染药物使用_6_用药频率,"【医嘱】条件①成功提取【抗感染药物使用_6_药物名称】后，提取【抗感染药物使用_6_药物名称】对应的{频次}，后输出（ ）次/每日，的数值，（例，“间隔8小时”，则输出“3”）；条件②{频次}内容为【立即执行】时，忽略；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_472,,抗感染药物使用_6_开始时间,"【医嘱】条件①成功提取【抗感染药物使用_6_药物名称】后；条件②提取【抗感染药物使用_6_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最早的{开始时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_473,,抗感染药物使用_6_结束时间,"【医嘱】条件①成功提取【抗感染药物使用_6_药物名称】后；条件②提取【抗感染药物使用_6_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最后一次该药物的用药医嘱{结束时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_474,,抗感染药物使用_6_给药途径(口服=1/静脉=2/雾化=3),"【医嘱】条件①成功提取【抗感染药物使用_6_药物名称】后；提取【抗感染药物使用_6_药物名称】对应的{用法}并归类为（口服=1/静脉=2/雾化=3）；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱；条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_475,,激素使用_1_药物名称,"目标： 从医疗记录中提取患者在特定时间段内使用的激素信息，并将其标准化为通用名。
条件：
①激素识别与标准化：
1.触发时间： 在【病案首页】中的【重症监护室进入时间1】的日期时间之后。
2.识别字段： 【医嘱】中的 {医嘱名称} 字段。
3.识别规则： 识别 {医嘱名称} 内的用药医嘱是否包含以下任一药物名称（包括商品名）： 【商品名通用名映射表】
4.标准化输出： 对于识别到的药物，务必使用提供的商品名与通用名映射表将其标准化为通用名进行输出。
5.映射规则： 
1.优先查找映射表中是否存在与 {医嘱名称}匹配的商品名。
2.如果找到匹配的商品名，则输出其对应的通用名。
3.特殊情况： 如果 {医嘱名称} 中的药物名称在提供的映射表中没有找到对应的条目，则直接输出{医嘱名称}中的原始文本（即商品名）。
6.商品名通用名映射表：格式（通用名：商品名）
 {氢化可的松：尤卓尔
可的松：可的松片
泼尼松：强的松
泼尼松龙：强的松龙
甲泼尼龙：甲强龙、美卓乐、舒禄-美卓乐
曲安西龙：阿赛松、康宁克通
地塞米松：德萨美、思诺迪清、利美达松
倍他米松：得宝松、贝施利
复方倍他米松：得宝松}
②多重用药记录：
1.如果患者使用了多种符合条件的激素，则按照其使用的时间顺序，依次填入预设的字段中，例如【激素使用_1_药物名称】、【激素使用_2_药物名称】……【激素使用_6_药物名称】。
2.时间的判断依据为【医嘱】中的开始时间。
③药物洗脱判断：
1.对于同一种激素（以标准化后的通用名为准），如果两次医嘱使用的时间间隔超过24小时（以上一次医嘱的结束时间和下一次医嘱的开始时间计算），则认为该药物发生洗脱。
2.洗脱后再次使用的该药物，将被视为下一次不同的用药，并记录在下一个【激素使用_x_药物名称】字段中。
④无效医嘱排除：
1.如果识别到【医嘱】中 {用法} 给药途径为静脉途径类的医嘱，并且该医嘱记录的 {开始时间} 与 {结束时间} 之间的间隔小于等于10分钟，则该条医嘱被认为是无效医嘱，不应记录。
2.【医嘱】中 {用法} 给药途径为皮内注射的算作皮试，不记录。
⑤剂量与频次变化处理：
1.如果同一种药物（以标准化后的通用名为准）的剂量或每日给药频次发生改变，则视为两种不同的用药，将被视为下一次不同的用药，并记录在下一个【激素使用_x_药物名称】字段中。",,,
ETL_CJ_476,,激素使用_1_剂量,"【医嘱】条件①成功提取【激素使用_1_药物名称】后，提取【激素使用_1_药物名称】对应的{剂量}数值出来，填入；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱。条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_477,,激素使用_1_用药频率,"【医嘱】条件①成功提取【激素使用_1_药物名称】后，提取【激素使用_1_药物名称】对应的{频次}，后输出（ ）次/每日，的数值，（例，“间隔8小时”，则输出“3”）；条件②{频次}内容为【立即执行】时，忽略；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_478,,激素使用_1_开始时间,"【医嘱】条件①成功提取【激素使用_1_药物名称】后；条件②提取【激素使用_1_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最早的{开始时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_479,,激素使用_1_结束时间,"【医嘱】条件①成功提取【激素使用_1_药物名称】后；条件②提取【激素使用_1_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最后一次该药物的用药医嘱{结束时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_480,,激素使用_1_给药途径(口服=1/静脉=2/雾化=3),"【医嘱】条件①成功提取【激素使用_1_药物名称】后；提取【激素使用_1_药物名称】对应的{用法}并归类为（口服=1/静脉=2/雾化=3）；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱；条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_481,,激素使用_2_药物名称,同【激素使用_1_药物名称】,,,
ETL_CJ_482,,激素使用_2_剂量,"【医嘱】条件①成功提取【激素使用_2_药物名称】后，提取【激素使用_2_药物名称】对应的{剂量}数值出来，填入；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱。条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_483,,激素使用_2_用药频率,"【医嘱】条件①成功提取【激素使用_2_药物名称】后，提取【激素使用_2_药物名称】对应的{频次}，后输出（ ）次/每日，的数值，（例，“间隔8小时”，则输出“3”）；条件②{频次}内容为【立即执行】时，忽略；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_484,,激素使用_2_开始时间,"【医嘱】条件①成功提取【激素使用_2_药物名称】后；条件②提取【激素使用_2_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最早的{开始时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_485,,激素使用_2_结束时间,"【医嘱】条件①成功提取【激素使用_2_药物名称】后；条件②提取【激素使用_2_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最后一次该药物的用药医嘱{结束时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_486,,激素使用_2_给药途径(口服=1/静脉=2/雾化=3),"【医嘱】条件①成功提取【激素使用_2_药物名称】后；提取【激素使用_2_药物名称】对应的{用法}并归类为（口服=1/静脉=2/雾化=3）；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱；条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_487,,激素使用_3_药物名称,同【激素使用_1_药物名称】,,,
ETL_CJ_488,,激素使用_3_剂量,"【医嘱】条件①成功提取【激素使用_3_药物名称】后，提取【激素使用_3_药物名称】对应的{剂量}数值出来，填入；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱。条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_489,,激素使用_3_用药频率,"【医嘱】条件①成功提取【激素使用_3_药物名称】后，提取【激素使用_3_药物名称】对应的{频次}，后输出（ ）次/每日，的数值，（例，“间隔8小时”，则输出“3”）；条件②{频次}内容为【立即执行】时，忽略；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_490,,激素使用_3_开始时间,"【医嘱】条件①成功提取【激素使用_3_药物名称】后；条件②提取【激素使用_3_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最早的{开始时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_491,,激素使用_3_结束时间,"【医嘱】条件①成功提取【激素使用_3_药物名称】后；条件②提取【激素使用_3_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最后一次该药物的用药医嘱{结束时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_492,,激素使用_3_给药途径(口服=1/静脉=2/雾化=3),"【医嘱】条件①成功提取【激素使用_3_药物名称】后；提取【激素使用_3_药物名称】对应的{用法}并归类为（口服=1/静脉=2/雾化=3）；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱；条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_493,,激素使用_4_药物名称,同【激素使用_1_药物名称】,,,
ETL_CJ_494,,激素使用_4_剂量,"【医嘱】条件①成功提取【激素使用_4_药物名称】后，提取【激素使用_4_药物名称】对应的{剂量}数值出来，填入；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱。条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_495,,激素使用_4_用药频率,"【医嘱】条件①成功提取【激素使用_4_药物名称】后，提取【激素使用_4_药物名称】对应的{频次}，后输出（ ）次/每日，的数值，（例，“间隔8小时”，则输出“3”）；条件②{频次}内容为【立即执行】时，忽略；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_496,,激素使用_4_开始时间,"【医嘱】条件①成功提取【激素使用_4_药物名称】后；条件②提取【激素使用_4_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最早的{开始时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_497,,激素使用_4_结束时间,"【医嘱】条件①成功提取【激素使用_4_药物名称】后；条件②提取【激素使用_4_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最后一次该药物的用药医嘱{结束时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_498,,激素使用_4_给药途径(口服=1/静脉=2/雾化=3),"【医嘱】条件①成功提取【激素使用_4_药物名称】后；提取【激素使用_4_药物名称】对应的{用法}并归类为（口服=1/静脉=2/雾化=3）；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱；条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_499,,激素使用_5_药物名称,同【激素使用_1_药物名称】,,,
ETL_CJ_500,,激素使用_5_剂量,"【医嘱】条件①成功提取【激素使用_5_药物名称】后，提取【激素使用_5_药物名称】对应的{剂量}数值出来，填入；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱。条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_501,,激素使用_5_用药频率,"【医嘱】条件①成功提取【激素使用_5_药物名称】后，提取【激素使用_5_药物名称】对应的{频次}，后输出（ ）次/每日，的数值，（例，“间隔8小时”，则输出“3”）；条件②{频次}内容为【立即执行】时，忽略；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_502,,激素使用_5_开始时间,"【医嘱】条件①成功提取【激素使用_5_药物名称】后；条件②提取【激素使用_5_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最早的{开始时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_503,,激素使用_5_结束时间,"【医嘱】条件①成功提取【激素使用_5_药物名称】后；条件②提取【激素使用_5_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最后一次该药物的用药医嘱{结束时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_504,,激素使用_5_给药途径(口服=1/静脉=2/雾化=3),"【医嘱】条件①成功提取【激素使用_5_药物名称】后；提取【激素使用_5_药物名称】对应的{用法}并归类为（口服=1/静脉=2/雾化=3）；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱；条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_505,,激素使用_6_药物名称,同【激素使用_1_药物名称】,,,
ETL_CJ_506,,激素使用_6_剂量,"【医嘱】条件①成功提取【激素使用_6_药物名称】后，提取【激素使用_6_药物名称】对应的{剂量}数值出来，填入；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱。条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_507,,激素使用_6_用药频率,"【医嘱】条件①成功提取【激素使用_6_药物名称】后，提取【激素使用_6_药物名称】对应的{频次}，后输出（ ）次/每日，的数值，（例，“间隔8小时”，则输出“3”）；条件②{频次}内容为【立即执行】时，忽略；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_508,,激素使用_6_开始时间,"【医嘱】条件①成功提取【激素使用_6_药物名称】后；条件②提取【激素使用_6_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最早的{开始时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_509,,激素使用_6_结束时间,"【医嘱】条件①成功提取【激素使用_6_药物名称】后；条件②提取【激素使用_6_药物名称】在-【病案首页】-[重症监护室进入时间1]的日期时间后，对应的最后一次该药物的用药医嘱{结束时间}；条件③若多次医嘱是同一{上述药物名称}中的药物名称，若后一次{开始时间}与上一次医嘱{结束时间}间隔≦24h,则算作一次用药，若后一次{开始时间}与上一次医嘱{结束时间}间隔≧24h则算作两种不同的用药；条件④若识别为静脉途径医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱，给药途径若为备用，则记为无效医嘱。条件⑤若剂量改变算作两种不同的用药。",,,
ETL_CJ_510,,激素使用_6_给药途径(口服=1/静脉=2/雾化=3),"【医嘱】条件①成功提取【激素使用_6_药物名称】后；提取【激素使用_6_药物名称】对应的{用法}并归类为（口服=1/静脉=2/雾化=3）；条件②若识别为静脉医嘱,且{开始时间}与{结束时间}间隔<10min，则记为无效医嘱；条件③给药途径若为备用，则记为无效医嘱。",,,
ETL_CJ_511,,容量管理_D1_总入量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第1天；条件②在【出入量】中查找[24小时出入量]中记录的是{入量}对应的{值}，提取数值。,,,
ETL_CJ_512,,容量管理_D1_总出量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第1天；条件②在【出入量】中查找[24小时出入量]中记录的是{出量}对应的{值}，提取数值。,,,
ETL_CJ_513,,容量管理_D1_总平衡,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第1天提取的{总入量}{总出量}；条件②总平衡量=总入量−总出量,,,
ETL_CJ_514,,容量管理_D2_总入量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第2天；条件②在【出入量】中查找[24小时出入量]中记录的是{入量}对应的{值}，提取数值。,,,
ETL_CJ_515,,容量管理_D2_总出量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第2天；条件②在【出入量】中查找[24小时出入量]中记录的是{出量}对应的{值}，提取数值。,,,
ETL_CJ_516,,容量管理_D2_总平衡,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第2天提取的{总入量}{总出量}；条件②总平衡量=总入量−总出量,,,
ETL_CJ_517,,容量管理_D3_总入量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第3天；条件②在【出入量】中查找[24小时出入量]中记录的是{入量}对应的{值}，提取数值。,,,
ETL_CJ_518,,容量管理_D3_总出量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第3天；条件②在【出入量】中查找[24小时出入量]中记录的是{出量}对应的{值}，提取数值。,,,
ETL_CJ_519,,容量管理_D3_总平衡,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第3天提取的{总入量}{总出量}；条件②总平衡量=总入量−总出量,,,
ETL_CJ_520,,容量管理_D4_总入量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第4天；条件②在【出入量】中查找[24小时出入量]中记录的是{入量}对应的{值}，提取数值。,,,
ETL_CJ_521,,容量管理_D4_总出量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第4天；条件②在【出入量】中查找[24小时出入量]中记录的是{出量}对应的{值}，提取数值。,,,
ETL_CJ_522,,容量管理_D4_总平衡,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第4天提取的{总入量}{总出量}；条件②总平衡量=总入量−总出量,,,
ETL_CJ_523,,容量管理_D5_总入量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第5天；条件②在【出入量】中查找[24小时出入量]中记录的是{入量}对应的{值}，提取数值。,,,
ETL_CJ_524,,容量管理_D5_总出量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第5天；条件②在【出入量】中查找[24小时出入量]中记录的是{出量}对应的{值}，提取数值。,,,
ETL_CJ_525,,容量管理_D5_总平衡,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第5天提取的{总入量}{总出量}；条件②总平衡量=总入量−总出量,,,
ETL_CJ_526,,容量管理_D6_总入量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第6天；条件②在【出入量】中查找[24小时出入量]中记录的是{入量}对应的{值}，提取数值。,,,
ETL_CJ_527,,容量管理_D6_总出量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第6天；条件②在【出入量】中查找[24小时出入量]中记录的是{出量}对应的{值}，提取数值。,,,
ETL_CJ_528,,容量管理_D6_总平衡,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第6天提取的{总入量}{总出量}；条件②总平衡量=总入量−总出量,,,
ETL_CJ_529,,容量管理_D7_总入量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第7天；条件②在【出入量】中查找[24小时出入量]中记录的是{入量}对应的{值}，提取数值。,,,
ETL_CJ_530,,容量管理_D7_总出量,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第7天；条件②在【出入量】中查找[24小时出入量]中记录的是{出量}对应的{值}，提取数值。,,,
ETL_CJ_531,,容量管理_D7_总平衡,【出入量】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]第7天提取的{总入量}{总出量}；条件②总平衡量=总入量−总出量,,,
ETL_CJ_532,,呼吸力学_D1_气道阻力,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【呼吸支持】中查找[名称]-{Raw}，提取对应的{值}中的数值，若有多组数据，提取最大的数值。,,,
ETL_CJ_533,,呼吸力学_D1_静态顺应性,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【呼吸支持】中查找[名称]-{Cst}，提取对应的{值}中的数值，若有多组数据，提取最大的数值。,,,
ETL_CJ_534,,呼吸力学_D1_气道平台压,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【呼吸支持】中查找[名称]-{Pplat}，提取对应的{值}中的数值，若有多组数据，提取最大的数值。,,,
ETL_CJ_535,,呼吸力学_D1_气道峰压,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【呼吸支持】中查找[名称]-{Ppeak}，提取对应的{值}中的数值，若有多组数据，提取最大的数值。,,,
ETL_CJ_536,,呼吸力学_D1_驱动压,‘=[呼吸力学_D1_气道平台压]-[呼吸力学_D1_PEEP]’,,,
ETL_CJ_537,,呼吸力学_D1_PEEP,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【呼吸支持】中查找[名称]-{PEEP}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。,,,
ETL_CJ_538,,呼吸力学_D1_潮气量,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【呼吸支持】中查找[名称]-{VT}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。,,,
ETL_CJ_539,,呼吸力学_D1_分钟通气量,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【呼吸支持】中查找[名称]-{MVe}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。,,,
ETL_CJ_540,,呼吸力学_D1_机械能_RR,"0.098*[RR]*[呼吸力学_D1_潮气量]*([呼吸力学_D1_气道峰压]-0.5*[呼吸力学_D1_驱动压])
【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【呼吸支持】中查找[名称]-{RR}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。",,,
ETL_CJ_541,,呼吸力学_D1_通气比,"‘= （[【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后24h内；条件②在【呼吸支持】中查找[名称]-{MVe}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。]×[【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的后24h内；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pCO2”的记录{pCO2}的对应的{值}；③若有多次测量值，选择最大值的数值。]）/(PBW×100×37.5)’
男性：PBW=50+0.91×(【护理记录】身高cm−152.4)【病案首页】-{性别}
女性：PBW=45.5+0.91×(【护理记录】身高cm−152.4)【病案首页】-{性别}",,,
ETL_CJ_542,,呼吸力学_D4_气道阻力,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第4天24h内；条件②在【呼吸支持】中查找[名称]-{Raw}，提取对应的{值}中的数值，若有多组数据，提取最大的数值。,,,
ETL_CJ_543,,呼吸力学_D4_静态顺应性,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第4天24h内；条件②在【呼吸支持】中查找[名称]-{Cst}，提取对应的{值}中的数值，若有多组数据，提取最大的数值。,,,
ETL_CJ_544,,呼吸力学_D4_气道平台压,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第4天24h内；条件②在【呼吸支持】中查找[名称]-{Pplat}，提取对应的{值}中的数值，若有多组数据，提取最大的数值。,,,
ETL_CJ_545,,呼吸力学_D4_气道峰压,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第4天24h内；条件②在【呼吸支持】中查找[名称]-{Ppeak}，提取对应的{值}中的数值，若有多组数据，提取最大的数值。,,,
ETL_CJ_546,,呼吸力学_D4_驱动压,‘=[呼吸力学_D4_气道平台压]-[呼吸力学_D4_PEEP]’,,,
ETL_CJ_547,,呼吸力学_D4_PEEP,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第4天24h内；条件②在【呼吸支持】中查找[名称]-{PEEP}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。,,,
ETL_CJ_548,,呼吸力学_D4_潮气量,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第4天24h内；条件②在【呼吸支持】中查找[名称]-{VT}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。,,,
ETL_CJ_549,,呼吸力学_D4_分钟通气量,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第4天24h内；条件②在【呼吸支持】中查找[名称]-{MVe}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。,,,
ETL_CJ_550,,呼吸力学_D4_机械能_RR,"0.098*[RR]*[呼吸力学_D4_潮气量]*([呼吸力学_D4_气道峰压]-0.5*[呼吸力学_D4_驱动压])
【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第4天24h内；条件②在【呼吸支持】中查找[名称]-{RR}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。",,,
ETL_CJ_551,,呼吸力学_D4_通气比,"‘= （[【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第4天24h内；条件②在【呼吸支持】中查找[名称]-{MVe}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。]×[【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第4天前后24h内；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pCO2”的记录{pCO2}的对应的{值}；③若有多次测量值，选择最大值的数值。]）/(PBW×100×37.5)’
男性：PBW=50+0.91×(【护理记录】身高cm−152.4)【病案首页】-{性别}
女性：PBW=45.5+0.91×(【护理记录】身高cm−152.4)【病案首页】-{性别}",,,
ETL_CJ_552,,呼吸力学_D7_气道阻力,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第7天24h内；条件②在【呼吸支持】中查找[名称]-{Raw}，提取对应的{值}中的数值，若有多组数据，提取最大的数值。,,,
ETL_CJ_553,,呼吸力学_D7_静态顺应性,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第7天24h内；条件②在【呼吸支持】中查找[名称]-{Cst}，提取对应的{值}中的数值，若有多组数据，提取最大的数值。,,,
ETL_CJ_554,,呼吸力学_D7_气道平台压,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第7天24h内；条件②在【呼吸支持】中查找[名称]-{Pplat}，提取对应的{值}中的数值，若有多组数据，提取最大的数值。,,,
ETL_CJ_555,,呼吸力学_D7_气道峰压,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第7天24h内；条件②在【呼吸支持】中查找[名称]-{Ppeak}，提取对应的{值}中的数值，若有多组数据，提取最大的数值。,,,
ETL_CJ_556,,呼吸力学_D7_驱动压,‘=[呼吸力学_D7_气道平台压]-[呼吸力学_D7_PEEP]’,,,
ETL_CJ_557,,呼吸力学_D7_PEEP,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第7天24h内；条件②在【呼吸支持】中查找[名称]-{PEEP}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。,,,
ETL_CJ_558,,呼吸力学_D7_潮气量,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第7天24h内；条件②在【呼吸支持】中查找[名称]-{VT}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。,,,
ETL_CJ_559,,呼吸力学_D7_分钟通气量,【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第7天24h内；条件②在【呼吸支持】中查找[名称]-{MVe}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。,,,
ETL_CJ_560,,呼吸力学_D7_机械能_RR,"0.098*[RR]*[呼吸力学_D7_潮气量]*([呼吸力学_D7_气道峰压]-0.5*[呼吸力学_D7_驱动压])
【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第7天24h内；条件②在【呼吸支持】中查找[名称]-{RR}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。",,,
ETL_CJ_561,,呼吸力学_D7_通气比,"‘= （[【呼吸支持】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]第7天24h内；条件②在【呼吸支持】中查找[名称]-{MVe}，提取对应的{值}中的数值。若有多组数据，提取最大的数值。]×[【血气】条件①[时间]在-【病案首页】-[重症监护室进入时间1]的日期后的第7天前后24h内；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pCO2”的记录{pCO2}的对应的{值}；③若有多次测量值，选择最大值的数值。]）/(PBW×100×37.5)’
男性：PBW=50+0.91×(【护理记录】身高cm−152.4)【病案首页】-{性别}
女性：PBW=45.5+0.91×(【护理记录】身高cm−152.4)【病案首页】-{性别}",,,
ETL_CJ_562,,并发症及支持治疗_有创呼吸支持_有创呼吸机(有=1/无=0),【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含含义有创呼吸机数据（eg.持续呼吸机辅助呼吸（有创）），若“有”则记1，“无”记0,,,
ETL_CJ_563,,并发症及支持治疗_有创呼吸支持_开始时间,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含含义有创呼吸机数据（eg.持续呼吸机辅助呼吸（有创）），提取其{开始时间}的数据，若有多组数据，提取最早的时间。,,,
ETL_CJ_564,,并发症及支持治疗_有创呼吸支持_结束时间,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含含义有创呼吸机数据（eg.持续呼吸机辅助呼吸（有创）），提取其{结束时间}的数据，若有多组数据，提取最晚的时间。,,,
ETL_CJ_565,,并发症及支持治疗_有创呼吸支持_气管切开(有=1/无=0),【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含含义气管切开（eg.气管切开术），提取其{结束时间}的数据，若“有”则记1，“无”记0,,,
ETL_CJ_566,,并发症及支持治疗_有创呼吸支持_操作时间,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含含义气管切开（eg.气管切开术），提取其{开始时间}的数据，若有多组数据，均提取。,,,
ETL_CJ_567,,并发症及支持治疗_有创呼吸支持_ECMO(有=1/无=0),【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含含义(ECMO安装)（eg.体外人工膜肺(ECMO)安装术），若“有”则记1，“无”记0。,,,
ETL_CJ_568,,并发症及支持治疗_有创呼吸支持_开始时间ECMO,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含含义(体外人工膜肺(ECMO)安装术)（eg.体外人工膜肺(ECMO)安装术），提取其{开始时间}的数据，若有多组数据，提取最早的时间。,,,
ETL_CJ_569,,并发症及支持治疗_有创呼吸支持_结束时间ECMO,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含含义体外膜肺（ECMO）撤除术（eg.体外膜肺（ECMO）撤除术），提取其{结束时间}的数据，若有多组数据，提取最晚的时间。,,,
ETL_CJ_570,,并发症及支持治疗_ARDS(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“呼吸窘迫”“ards”“ARDS”字段（eg.急性呼吸窘迫综合症（重度））且{入院诊断}中不包含“呼吸窘迫”“ards”“ARDS”字段（eg.急性呼吸窘迫综合症（重度））或者包含{重症肺炎}+{I型呼吸衰竭}，若“有”则记1，“无”记0,,,
ETL_CJ_571,,并发症及支持治疗_脓毒症_Sepsis(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“脓毒症”且{入院诊断}中不包含“脓毒症”，若“有”则记1，“无”记0,,,
ETL_CJ_572,,并发症及支持治疗_脓毒症_Septic Shock(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“脓毒性休克”且{入院诊断}中不包含“脓毒性休克”，若“有”则记1，“无”记0,,,
ETL_CJ_573,,并发症及支持治疗_脓毒症_血管活性药物(有=1/无=0),"目标： 从医疗记录中提取患者在特定时间段内使用的血管活性药物信息，若“有”则记1，“无”记0
条件：
1.触发时间：在【病案首页】中的【重症监护室进入时间1】的日期时间之后。
2.识别字段：【医嘱】中的 {医嘱名称} 字段。
3.识别规则：识别 {医嘱名称} 内的用药医嘱是否包含以下任一药物名称（包括商品名）：{去甲肾上腺素/间羟胺/多巴胺/多巴酚丁胺}
4.输出：对于识别到药物，若“有”则记1，“无”记0",,,
ETL_CJ_574,,并发症及支持治疗_脓毒症_血管活性药物开始时间,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-{血管活性药物信息}，提取其{开始时间}的数据，若有多组数据，提取最早的时间。,,,
ETL_CJ_575,,并发症及支持治疗_脓毒症_血管活性药物结束时间,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-{血管活性药物信息}，提取其{结束时间}的数据，若有多组数据，提取最早的时间。,,,
ETL_CJ_576,,并发症及支持治疗_血流感染_血流感染(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“血流感染”字段（eg.血流感染（铜绿假单胞菌及革兰阳性球菌））但不包括“导管相关血流感染”（eg.导管相关血流感染（洋葱伯克霍尔德菌））且{入院诊断}中不包含“血流感染”，若“有”则记1，“无”记0,,,
ETL_CJ_577,,并发症及支持治疗_急性肾损伤(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“急性肾损伤”字段（eg.急性肾损伤，急性肾功能损伤）且{入院诊断}中不包含“急性肾损伤”字段;或者【实验室检查数据】条件①[报告日期]在-【病案首页】-[重症监护室进入时间1]后；条件②在【实验室检查数据】中查找[检验指标名]中记录的是{★肌酐（酶法）(★CR)}对应的{检验指标值}，连续两天>26，若“有”则记1，“无”记0,,,
ETL_CJ_578,,并发症及支持治疗_CRRT(有=1/无=0),【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[用法]-CRRT，若“有”则记1，“无”记0,,,
ETL_CJ_579,,并发症及支持治疗_CRRT_开始时间,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[用法]-CRRT，若“有”，则记录最早的【开始时间】，输出格式为YYYYMMDD。,,,
ETL_CJ_580,,并发症及支持治疗_CRRT_结束时间,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[用法]-CRRT，若“有”，则记录最晚的时间，来源【开始时间】或【结束时间】，输出格式为YYYYMMDD。,,,
ETL_CJ_581,,并发症及支持治疗_急性肝损伤(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“急性肝损伤”字段（eg.急性肝损伤，急性肝功能损伤）且{入院诊断}中不包含“急性肝损伤”字段，若“有”则记1，“无”记0,,,
ETL_CJ_582,,并发症及支持治疗_人工肝(有=1/无=0),【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含“血液灌流”or“血液灌流(HP)，若“有”则记1，“无”记0,,,
ETL_CJ_583,,并发症及支持治疗_DIC(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“血管内凝血”字段（eg.弥散性血管内凝血，弥漫性血管内凝血）且{入院诊断}中不包含“血管内凝血”字段，若“有”则记1，“无”记0,,,
ETL_CJ_584,,并发症及支持治疗_急性心血管事件(有=1/无=0),在【电子病历】中查找[出院诊断]-包含{冠脉综合征，急性心肌梗死，恶性心律失常（引起血流动力学不稳定的心律失常），急性心功能不全、急性肺栓塞、急性深静脉血栓形成}且{入院诊断}中不包含提取到的诊断，若“有”则记1，“无”记0,,,
ETL_CJ_585,,并发症及支持治疗_急性心肌梗死(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“急性心肌梗死”字段（eg.急性心肌梗死）且{入院诊断}中不包含“急性心肌梗死”字段，若“有”则记1，“无”记0,,,
ETL_CJ_586,,并发症及支持治疗_急性心律失常(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“急性心律失常”字段（eg.急性心律失常）且{入院诊断}中不包含“急性心律失常”字段，若“有”则记1，“无”记0,,,
ETL_CJ_587,,并发症及支持治疗_急性肺栓塞(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“急性肺栓塞”字段（eg.急性肺栓塞（高危组））且{入院诊断}中不包含“急性肺栓塞”字段，若“有”则记1，“无”记0,,,
ETL_CJ_588,,并发症及支持治疗_急性心功能不全(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“急性肺栓塞”字段（eg.急性肺栓塞（高危组））且{入院诊断}中不包含“急性肺栓塞”字段，若“有”则记1，“无”记0,,,
ETL_CJ_589,,并发症及支持治疗_急性脑血管意外(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“脑梗死”、“脑出血、“蛛网膜下腔出血”字段（eg.脑出血 蛛网膜下腔出血）且{入院诊断}中不包含与[出院诊断]同样的“脑梗死”、“脑出血、“蛛网膜下腔出血”字段，若“有”则记1，“无”记0,,,
ETL_CJ_590,,并发症及支持治疗_急性脑出血(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“脑出血”字段（eg.急性脑出血）且{入院诊断}中不包含“脑出血”字段，若“有”则记1，“无”记0,,,
ETL_CJ_591,,并发症及支持治疗_急性脑梗死(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“脑梗死”字段（eg.急性脑梗死）且{入院诊断}中不包含“脑梗死”字段，若“有”则记1，“无”记0,,,
ETL_CJ_592,,并发症及支持治疗_院内感染_医院获得性肺炎(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“医院获得性肺炎”字段（eg.重症医院获得性肺炎 （肺炎克雷伯杆菌、铜绿假单胞菌感染））或[出院诊断]-包含‘肺炎’诊断后描述中有【鲍曼不动杆菌，铜绿假单胞菌，肺炎克雷伯杆菌，嗜麦芽窄食单胞菌，纹带棒杆菌，木氧化无色杆菌，烟曲霉，屎肠球菌，金黄色葡萄球菌，白念珠菌，脑膜脓毒伊丽莎白菌】且{入院诊断}中不包含识别到的该诊断，若“有”则记1，“无”记0,,,
ETL_CJ_593,,并发症及支持治疗_院内感染_医院获得性肺炎_病原学,在【电子病历】中查找[出院诊断]-包含“医院获得性肺炎”字段（eg.重症医院获得性肺炎 （肺炎克雷伯杆菌、铜绿假单胞菌感染））或[出院诊断]-包含‘肺炎’诊断后描述中有【鲍曼不动杆菌，铜绿假单胞菌，肺炎克雷伯杆菌，嗜麦芽窄食单胞菌，纹带棒杆菌，木氧化无色杆菌，烟曲霉，屎肠球菌，金黄色葡萄球菌，白念珠菌，脑膜脓毒伊丽莎白菌】的数据完成{并发症及支持治疗_院内感染_医院获得性肺炎(有=1/无=0}的题目后，提取其肺炎病原菌描述。（eg.重症医院获得性肺炎 （肺炎克雷伯杆菌、铜绿假单胞菌感染）本题输出答案是：肺炎克雷伯杆菌、铜绿假单胞菌）,,,
ETL_CJ_594,,并发症及支持治疗_院内感染_导管相关血流感染(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“导管相关血流感染”字段（eg.导管相关血流感染（洋葱伯克霍尔德菌））且{入院诊断}中不包含“导管相关血流感染”，若“有”则记1，“无”记0,,,
ETL_CJ_595,,并发症及支持治疗_院内感染_导管相关血流感染_病原学,在【电子病历】中查找[出院诊断]-包含“导管相关血流感染”字段（eg.导管相关血流感染（洋葱伯克霍尔德菌））的数据完成{并发症及支持治疗_院内感染_导管相关血流感染(有=1/无=0)}的题目后，提取其肺炎病原菌描述。（eg.导管相关血流感染（洋葱伯克霍尔德菌）本题输出答案是：洋葱伯克霍尔德菌）,,,
ETL_CJ_596,,并发症及支持治疗_院内感染_泌尿系感染(有=1/无=0),在【电子病历】中查找[出院诊断]-一诊断名称内包含（“泌尿”or“尿路”or“肾”or“膀胱”or“尿道”+“感染”or“炎”）字段（eg.导管相关尿路感染 真菌性泌尿道感染）且{入院诊断}中不包含一诊断名称内包含（“泌尿”or“尿路”or“肾”or“膀胱”or“尿道”+“感染”or“炎”）字段，若“有”则记1，“无”记0,,,
ETL_CJ_597,,并发症及支持治疗_院内感染_泌尿系感染_病原学,在【电子病历】中查找[出院诊断]-一诊断名称内包含（“泌尿”or“尿路”or“肾”or“膀胱”or“尿道”+“感染”or“炎”）字段（eg.尿路感染（耐碳青霉烯的肺炎克雷伯杆菌））的数据完成{并发症及支持治疗_院内感染_泌尿系感染(有=1/无=0)}的题目后，提取其肺炎病原菌描述。（eg.尿路感染（耐碳青霉烯的肺炎克雷伯杆菌）本题输出答案是：耐碳青霉烯的肺炎克雷伯杆菌）,,,
ETL_CJ_598,,并发症及支持治疗_院内感染_腹腔感染(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“腹腔感染”字段（eg.腹腔感染（艰难梭菌感染））且{入院诊断}中不包含“腹腔感染”，若“有”则记1，“无”记0,,,
ETL_CJ_599,,并发症及支持治疗_院内感染_腹腔感染_病原学,在【电子病历】中查找[出院诊断]-包含“腹腔感染”字段（eg.腹腔感染（艰难梭菌感染））的数据完成{并发症及支持治疗_院内感染_腹腔感染(有=1/无=0)}的题目后，提取其肺炎病原菌描述。（eg.腹腔感染（艰难梭菌感染）本题输出答案是：艰难梭菌）,,,
ETL_CJ_600,,并发症及支持治疗_院内感染_抗生素相关腹泻(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“抗生素相关腹泻”字段（eg.抗生素相关腹泻）且{入院诊断}中不包含“抗生素相关腹泻”，若“有”则记1，“无”记0,,,
ETL_CJ_601,,并发症及支持治疗_院内感染_其他院内感染(有=1/无=0),在【电子病历】中查找[出院诊断]-包含“感染”字段（eg.左侧胸腔感染（耐碳青霉烯肺炎克雷柏杆菌感染））and非‘医院获得性肺炎’‘导管相关血流感染’‘泌尿系感染’‘腹腔感染’‘抗生素相关腹泻’，且{入院诊断}中不包含该诊断，若“有”则记1，“无”记0,,,
ETL_CJ_602,,主要治疗_清醒俯卧位(是=1/否=0),"【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含气管插管术（eg.气管插管术）若无""气管插管术""且有【俯卧位通气治疗】，则记1，“不符合”记0",,,
ETL_CJ_603,,主要治疗_清醒俯卧位_启动日期,【医嘱】在{主要治疗_清醒俯卧位(是=1/否=0)}选择“1”后，记录【俯卧位通气治疗】最早的【开始时间】，输出格式为YYYYMMDD。,,,
ETL_CJ_604,,主要治疗_清醒俯卧位_终止日期,【医嘱】在{主要治疗_清醒俯卧位(是=1/否=0)}选择“1”后，记录【俯卧位通气治疗】最后的【结束时间】，输出格式为YYYYMMDD。,,,
ETL_CJ_605,,主要治疗_清醒俯卧位_清醒俯卧位前的PFR,【血气】条件①在{主要治疗_清醒俯卧位_启动日期}前最近的一次【血气】；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}；③将该数值除以与之有相同{血气ID}的{名称}记录{FiO2}的对应的{值}(if{Fio2}的值>=21是百分数，例如取值是100，实际数值应为100%；if{Fio2}的值属于（0.21-1）区间则作为小数直接计算，无需“%”规则介入，若取值范围在{0-0.20}，{1.01-20}则记为无效数据，不采用，本次输出结果为‘N/A’)，相除结果小数即为结果。,,,
ETL_CJ_606,,主要治疗_清醒俯卧位_清醒俯卧位前的SFR,条件①在{主要治疗_清醒俯卧位_启动日期}前最近的一次【血气】&【护理记录】；条件②在【护理记录】中查找[项目名称]-SpO₂，对应的[项目值(字符)]中的值，数值为百分比，需要加上“%”，（例如提取的数值为97，输出结果应为97%）在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，记录{FiO2}的对应的{值}(if{Fio2}的值>=21是百分数，例如取值是100，实际数值应为100%；if{Fio2}的值属于（0.21-1）区间则作为小数直接计算，无需“%”规则介入，若取值范围在{0-0.20}，{1.01-20}则记为无效数据，不采用，本次输出结果为‘N/A’)，相除结果小数即为结果。,,,
ETL_CJ_607,,主要治疗_气管插管_插管时间,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含气管插管术（eg.气管插管术），提取其{开始时间}的数据，若有多组数据，均提取，但后续题目如果使用此题结果引用“最早的一次插管时间”。,,,
ETL_CJ_608,,主要治疗_气管插管_插管前呼吸支持方式(1=HFNC/2=NPPV/3=普通氧疗),【呼吸支持】条件①在{主要治疗_气管插管_插管时间}结果时间前最近的一次呼吸支持方式；按{1=HFNC/2=NPPV/3=普通氧疗}，输出1、2、3，【呼吸支持】中【值】{HFNC：经鼻高流量；NPPV:NPPV面罩：储氧面罩，储氧面罩吸氧，普通面罩，文丘里，文丘里面罩，文丘里吸氧；鼻导管：鼻导管吸氧，鼻导管，经鼻高流量，Airvo，高流量}。,,,
ETL_CJ_609,,主要治疗_气管插管_IPPV前PFR,【血气】条件①在{主要治疗_气管插管_插管时间}结果时间前最近的；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}；③将该数值除以与之有相同{血气ID}的{名称}记录{FiO2}的对应的{值}(if{Fio2}的值>=21是百分数，例如取值是100，实际数值应为100%；if{Fio2}的值属于（0.21-1）区间则作为小数直接计算，无需“%”规则介入，若取值范围在{0-0.20}，{1.01-20}则记为无效数据，不采用，本次输出结果为‘N/A’)，相除结果小数即为结果。,,,
ETL_CJ_610,,主要治疗_气管插管_IPPV前SFR,条件①在{主要治疗_气管插管_插管时间}结果时间前最近的【血气】&【护理记录】；条件②在【护理记录】中查找[项目名称]-SpO₂，对应的[项目值(字符)]中的值，数值为百分比，需要加上“%”，（例如提取的数值为97，输出结果应为97%）在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，记录{FiO2}的对应的{值}(if{Fio2}的值>=21是百分数，例如取值是100，实际数值应为100%；if{Fio2}的值属于（0.21-1）区间则作为小数直接计算，无需“%”规则介入，若取值范围在{0-0.20}，{1.01-20}则记为无效数据，不采用，本次输出结果为‘N/A’)，相除结果小数即为结果。,,,
ETL_CJ_611,,主要治疗_气管插管_HFNC转为IPPV前ROX,条件①{主要治疗_气管插管_插管前呼吸支持方式(1=HFNC/2=NPPV/3=普通氧疗)}结果为“1”时，条件②用{主要治疗_气管插管_IPPV前SFR}结果除以【{主要治疗_气管插管_插管前呼吸支持方式(1=HFNC/2=NPPV/3=普通氧疗)}提取来源的同时间【呼吸支持】中查找[名称]-{RR}，提取对应的{值}中的数值】,,,
ETL_CJ_612,,HACOR-hr,【护理记录】条件①{主要治疗_气管插管_插管前呼吸支持方式(1=HFNC/2=NPPV/3=普通氧疗)}结果为“2”时，条件②用{主要治疗_气管插管_插管时间}前最近的一次【护理记录】条件③在【护理记录】中查找[项目名称]-[心率]+[脉搏]，对应的[项目值(数值)],,,
ETL_CJ_613,,HACOR-hr赋值,"<120→0
≥121→1",,,
ETL_CJ_614,,HACOR-ph,【血气】条件①{主要治疗_气管插管_插管前呼吸支持方式(1=HFNC/2=NPPV/3=普通氧疗)}结果为“2”时，条件②用{主要治疗_气管插管_插管时间}前最近的一次在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pH”的记录{pH}的对应的{值}。,,,
ETL_CJ_615,,HACOR-ph赋值,"≥7.35→0
7.30 ~ 7.34→2
7.25 ~ 7.29→3
<7.25→4",,,
ETL_CJ_616,,HACOR-pfr,【血气】条件①{主要治疗_气管插管_插管前呼吸支持方式(1=HFNC/2=NPPV/3=普通氧疗)}结果为“2”时，条件②条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}；③若有多次测量值，选择最小值的数值，再将该数值除以与之有相同{血气ID}的{名称}记录{FiO2}的对应的{值}(if{Fio2}的值>=21是百分数，例如取值是100，实际数值应为100%；if{Fio2}的值属于（0.21-1）区间则作为小数直接计算，无需“%”规则介入，若取值范围在{0-0.20}，{1.01-20}则记为无效数据，不采用，本次输出结果为‘N/A’)，相除结果小数即为结果。,,,
ETL_CJ_617,,HACOR-pfr赋值,"≥201→0
176~200→2
151~175→3
126 ~150→4
101~125→5
≤100→6",,,
ETL_CJ_618,,HACOR-rr,【护理记录】条件①{主要治疗_气管插管_插管前呼吸支持方式(1=HFNC/2=NPPV/3=普通氧疗)}结果为“2”时，条件②用{主要治疗_气管插管_插管时间}前最近的一次【护理记录】条件③在【护理记录】中查找[项目名称]-呼吸，对应的[项目值(数值)]。,,,
ETL_CJ_619,,HACOR-rr赋值,"≤30→0
31~35→1
36~40→2
45→3
≥46→4",,,
ETL_CJ_620,,主要治疗_气管插管_NPPV转为IPPV前HACOR,上5个“赋值”字段的得分结果总和,,,
ETL_CJ_621,,主要治疗_插管后俯卧位(是=1/否=0),【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含气管插管术（eg.气管插管术）若有气管插管术且有【俯卧位通气治疗】，则记1，“不符合”记0,,,
ETL_CJ_622,,主要治疗_插管后俯卧位_启动日期,【医嘱】在{主要治疗_插管后俯卧位(是=1/否=0)}选择“1”后，【医嘱】中查找[医嘱名称]-包含气管插管术（eg.气管插管术）{开始时间}后的，记录【俯卧位通气治疗】最早的【开始时间】，输出格式为YYYYMMDD。,,,
ETL_CJ_623,,主要治疗_插管后俯卧位_终止日期,【医嘱】在{主要治疗_插管后俯卧位(是=1/否=0)}选择“1”后，【医嘱】中查找[医嘱名称]-包含气管插管术（eg.气管插管术）{开始时间}后的，记录【俯卧位通气治疗】最后的【结束时间】，输出格式为YYYYMMDD。,,,
ETL_CJ_624,,主要治疗_ECMO(是=1/否=0),【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-包含ECMO（eg.体外膜肺氧合（ECMO）运动监测），若“有”则记1，“无”记0。,,,
ETL_CJ_625,,主要治疗_ECMO_启动日期,【医嘱】在{主要治疗_ECMO(是=1/否=0)}选择“1”后，记录【体外膜肺氧合（ECMO）运动监测】最早的【开始时间】，输出格式为YYYYMMDD。,,,
ETL_CJ_626,,主要治疗_ECMO_终止日期,【医嘱】在{主要治疗_ECMO(是=1/否=0)}选择“1”后，记录【体外膜肺氧合（ECMO）运动监测】最晚的【结束时间】，输出格式为YYYYMMDD。,,,
ETL_CJ_627,,主要治疗_ECMO_ECMO前PFR,【医嘱】条件①在{主要治疗_ECMO(是=1/否=0)}选择“1”后，记录【体外膜肺氧合（ECMO）运动监测】最早的【开始时间】，前6h内条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pO2”的记录{pO2}的对应的{值}；③将该数值除以与之有相同{血气ID}的{名称}记录{FiO2}的对应的{值}(if{Fio2}的值>=21是百分数，例如取值是100，实际数值应为100%；if{Fio2}的值属于（0.21-1）区间则作为小数直接计算，无需“%”规则介入，若取值范围在{0-0.20}，{1.01-20}则记为无效数据，不采用，本次输出结果为‘N/A’)，相除结果小数即为结果。,,,
ETL_CJ_628,,主要治疗_ECMO_ECMO前PaCO2,【医嘱】条件①在{主要治疗_ECMO(是=1/否=0)}选择“1”后，记录【体外膜肺氧合（ECMO）运动监测】最早的【开始时间】，前6h内；条件②在【血气】中查找[名称]中记录的是{Blood_Type}对应的{值}文本是{动脉}的，提取与之有相同{血气ID}的{名称}精确等于“pCO2”的记录{pCO2}的对应的{值}；③若有多次测量值，选择最大值的数值。,,,
ETL_CJ_629,,主要治疗_肌松剂使用(是=1/否=0),"目标： 从医疗记录中提取患者在特定时间段内使用的肌松剂信息，若“有”则记1，“无”记0
条件：
1.触发时间：在【病案首页】中的【重症监护室进入时间1】的日期时间之后。
2.识别字段：【医嘱】中的 {医嘱名称} 字段。
3.识别规则：识别 {医嘱名称} 内的用药医嘱是否包含以下任一药物名称（包括商品名）：
{罗库溴铵：Esmeron（爱可松）
维库溴铵：Norcuron（诺库隆）
泮库溴铵：Pavulon（帕福隆）
哌库溴铵：Arduan（阿杜安）
阿曲库铵：Tracrium（特级宁）
米库氯铵：Mivacron（美维宁）
琥珀胆碱：Anectine（安可宁）}
4.输出：对于识别到药物，若“有”则记1，“无”记0",,,
ETL_CJ_630,,主要治疗_肌松剂使用_启动日期,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-{肌松剂使用}，提取其{开始时间}的数据，若有多组数据，提取最早的时间。,,,
ETL_CJ_631,,主要治疗_肌松剂使用_终止日期,【医嘱】条件①[记录时间]在-【病案首页】-[重症监护室进入时间1]后；条件②在【医嘱】中查找[医嘱名称]-{肌松剂使用}，提取其{结束时间}的数据，若有多组数据，提取最晚的时间。,,,
ETL_CJ_632,,预后及随访_临床结局(1=好转出院/2=死亡/3=未好转),【病案首页】-{离院方式}将-{离院方式}数值1、2、3、4、5、9【1. 医嘱离院 2，医嘱转院，转入医疗机构 3. 医嘱转社区卫生服务机构 4. 非医嘱离院 5.死亡 9.其他】按这个映射规则转化成中文文本。,,,
ETL_CJ_633,,患者预后及随访_出ICU时间,"请根据【病案首页】提供的以下字段信息，计算并输出患者每次（合并后）的出ICU时间。

**输入字段:**
*   `重症监护室进入时间1`, `重症监护室退出时间1`
*   `重症监护室进入时间2`, `重症监护室退出时间2`
*   `重症监护室进入时间3`, `重症监护室退出时间3`
*   `重症监护室进入时间4`, `重症监护室退出时间4`
*   `重症监护室进入时间5`, `重症监护室退出时间5`

**计算规则:**
1.  **配对与排序:** 首先，将所有有效的进入时间和对应的退出时间配对，并按进入时间的先后顺序排列这些ICU入住记录。忽略无效或缺失的时间对。
2.  **合并判断:** 检查相邻两次ICU入住记录。如果某次ICU的“退出时间”与紧邻的下一次ICU的“进入时间”之间的时间间隔小于5小时（`下次进入时间 - 上次退出时间 < 5小时`），则这两次ICU入住视为一次连续的入住。
3.  **合并处理:**
    *   如果需要合并，则连续入住的“进入时间”取第一次入住的进入时间，连续入住的“退出时间”取最后一次入住的退出时间。
    *   重复此合并过程，直到所有满足条件的相邻入住记录都被合并。
4.  **输出要求:**
    *   **每次出ICU时间:** 对于每一段（合并后）的ICU入住，输出其最终的“重症监护室退出时间”。请以列表形式展示所有这些最终的退出时间点。

**示例逻辑（非具体数值，仅为演示合并思路）：**
*   记录1: 进A, 出A
*   记录2: 进B, 出B (出A到进B < 5小时) -> 合并为：进A, 出B
*   记录3: 进C, 出C (出B到进C >= 5小时) -> 独立记录：进C, 出C
*   记录4: 进D, 出D (出C到进D < 5小时) -> 合并为：进C, 出D
*   记录5: 进E, 出E (出D到进E < 5小时) -> 合并为：进C, 出E

则最终输出的出ICU时间列表为：[出B的时间点, 出E的时间点]",,,
ETL_CJ_634,,患者预后及随访_出院转院时间,【病案首页】{出院时间.1},,,
ETL_CJ_635,,患者预后及随访_出院转院死亡时主要诊断,在【电子病历】[出院诊断],,,
ETL_CJ_636,,患者预后及随访_住ICU时间,"请根据【病案首页】提供的以下字段信息，计算并输出患者每次（合并后）的住ICU时长以及总的住ICU时长。
*   `重症监护室进入时间1`, `重症监护室退出时间1`
*   `重症监护室进入时间2`, `重症监护室退出时间2`
*   `重症监护室进入时间3`, `重症监护室退出时间3`
*   `重症监护室进入时间4`, `重症监护室退出时间4`
*   `重症监护室进入时间5`, `重症监护室退出时间5`

**计算规则:**
1.  **配对与排序:** 首先，将所有有效的进入时间和对应的退出时间配对，并按进入时间的先后顺序排列这些ICU入住记录。忽略无效或缺失的时间对。
2.  **合并判断:** 检查相邻两次ICU入住记录。如果某次ICU的“退出时间”与紧邻的下一次ICU的“进入时间”之间的时间间隔小于5小时（`下次进入时间 - 上次退出时间 < 5小时`），则这两次ICU入住视为一次连续的入住。
3.  **合并处理:**
    *   如果需要合并，则连续入住的“进入时间”取第一次入住的进入时间，连续入住的“退出时间”取最后一次入住的退出时间。
    *   重复此合并过程，直到所有满足条件的相邻入住记录都被合并。
4.  **时长计算:**
    *   **单次住ICU时长:** 对于每一段（合并后）的ICU入住，其时长为该段的最终“重症监护室退出时间”减去该段的最初“重症监护室进入时间”。
    *   **总住ICU时长:** 将所有（合并后）单次住ICU时长相加。
5.  **输出要求:**
    *   **每次住ICU时长:** 以列表形式展示每一段（合并后）ICU入住的时长。
    *   **总住ICU时长:** 输出所有合并后住ICU时长的总和。

**示例逻辑（非具体数值，仅为演示合并和时长计算思路）：**
*   记录1: 进A (01-01 10:00), 出A (01-01 15:00) -> 时长1 = 5小时
*   记录2: 进B (01-01 18:00), 出B (01-01 22:00) (出A到进B = 3小时 < 5小时)
    *   合并处理：视为一次从 进A (01-01 10:00) 到 出B (01-01 22:00) 的入住。
    *   合并后单次时长 = 12小时。
*   记录3: 进C (01-02 08:00), 出C (01-02 18:00) (出B到进C = 10小时 >= 5小时)
    *   独立记录，单次时长 = 10小时。

则最终输出：
*   每次住ICU时长列表: [12小时, 10小时]
*   总住ICU时长: 22小时",,,
ETL_CJ_637,,患者预后及随访_住院时间,【病案首页】{出院时间}-{入院时间}结果为（）天,,,
ETL_CJ_638,,患者预后及随访_住院费用,【病案首页】{住院总费用},,,
